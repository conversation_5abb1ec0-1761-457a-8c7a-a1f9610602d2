<template>
  <div class="app-container">
    <ContentWrap title="评估应用">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="intro-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>欢迎使用评估应用</span>
              </div>
            </template>
            <div class="intro-content">
              <p>评估应用提供了便捷的评估工具，您可以选择单一模板评估或评估清单进行评估。</p>
              <p>请在下方选择您需要的评估方式，然后点击"开始评估"按钮。</p>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <div class="evaluation-tabs-container">
        <el-row :gutter="20" class="mt-20">
          <el-col :span="24">
            <el-tabs v-model="activeTab" type="card">
              <el-tab-pane label="模板评估" name="template">
                <TemplateSelector
                  ref="templateSelectorRef"
                  v-model:selectedTemplate="selectedTemplate"
                  @template-selected="handleTemplateSelected"
                />
              </el-tab-pane>
              <el-tab-pane label="清单评估" name="list">
                <ListSelector
                  ref="listSelectorRef"
                  v-model:selectedList="selectedList"
                  @list-selected="handleListSelected"
                />
              </el-tab-pane>
            </el-tabs>
          </el-col>
        </el-row>
      </div>

      <el-row :gutter="20" class="mt-20">
        <el-col :span="24" class="text-center">
          <el-button
            type="primary"
            size="large"
            :disabled="!canStartEvaluation"
            @click="startEvaluation"
          >
            开始评估
          </el-button>
        </el-col>
      </el-row>

      <!-- 评估对话框已移除，直接使用评估结果管理中的评估页面 -->

      <!-- 老人选择对话框 -->
      <el-dialog
        v-model="elderDialogVisible"
        title="选择评估老人"
        width="600px"
        destroy-on-close
      >
        <el-form :model="elderForm" label-width="100px">
          <el-form-item label="老人" prop="elderId" required>
            <el-select
              v-model="elderForm.elderId"
              filterable
              placeholder="请选择老人"
              style="width: 100%"
              @change="handleElderChange"
            >
              <el-option
                v-for="item in elderList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
                <div class="elder-option">
                  <span>{{ item.name }}</span>
                  <span class="elder-info">{{ '老人ID: ' + item.id }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="评估原因" prop="evaluationReason" required>
            <el-select v-model="elderForm.evaluationReason" placeholder="请选择评估原因" style="width: 100%">
              <el-option label="首次评估" value="首次评估" />
              <el-option label="定期评估" value="定期评估" />
              <el-option label="状态变化评估" value="状态变化评估" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="elderDialogVisible = false">取消</el-button>
            <el-button type="primary" :disabled="!elderForm.elderId || !elderForm.evaluationReason" @click="confirmElder">确认</el-button>
          </div>
        </template>
      </el-dialog>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import TemplateSelector from './components/TemplateSelector.vue'
import ListSelector from './components/ListSelector.vue'
import { useMessage } from '@/hooks/web/useMessage'
import { ListExecutionApi } from '@/api/evaluation/listexecution'
import { TemplateApi } from '@/api/evaluation/template'

defineOptions({ name: 'EvaluationApplication' })

const router = useRouter()
const message = useMessage()
const userStore = useUserStore()

// 当前激活的标签页
const activeTab = ref('template')

// 选中的模板和清单
const selectedTemplate = ref<any>(null)
const selectedList = ref<any>(null)

// 组件引用
const templateSelectorRef = ref()
const listSelectorRef = ref()

// 老人选择对话框
const elderDialogVisible = ref(false)
const elderList = ref<any[]>([])
const elderForm = ref({
  elderId: undefined as number | undefined,
  elderName: '',
  evaluationReason: ''
})

// 评估类型
const evaluationType = ref('')

// 不再需要评估对话框相关变量

// 判断是否可以开始评估
const canStartEvaluation = computed(() => {
  if (activeTab.value === 'template') {
    return !!selectedTemplate.value
  } else {
    return !!selectedList.value
  }
})

// 获取老人列表
const getElderList = async () => {
  try {
    const res = await ArchivesProfileApi.getArchivesProfileSimpleList()
    elderList.value = res
  } catch (error) {
    console.error('获取老人列表失败:', error)
    message.error('获取老人列表失败')
  }
}

// 处理模板选择
const handleTemplateSelected = (template) => {
  selectedTemplate.value = template
}

// 处理清单选择
const handleListSelected = (list) => {
  selectedList.value = list
}

// 处理老人选择变化
const handleElderChange = (id) => {
  const elder = elderList.value.find(item => item.id === id)
  if (elder) {
    elderForm.value.elderName = elder.name
  }
}

// 开始评估
const startEvaluation = () => {
  if (!selectedTemplate.value && !selectedList.value) {
    message.warning(`请先选择${activeTab.value === 'template' ? '模板' : '清单'}`)
    return
  }

  evaluationType.value = activeTab.value
  elderDialogVisible.value = true
}

// 确认老人选择
const confirmElder = () => {
  if (!elderForm.value.elderId || !elderForm.value.evaluationReason) {
    message.warning('请选择老人和评估原因')
    return
  }

  elderDialogVisible.value = false

  if (evaluationType.value === 'template') {
    // 跳转到单一模板评估页面
    router.push({
      path: '/evaluation/aiEval',
      query: {
        elderId: elderForm.value.elderId,
        templateId: selectedTemplate.value.id,
        templateName: selectedTemplate.value.name,
        evaluatorId: userStore.user.id,
        evaluatorName: userStore.user.nickname,
        evaluationReason: elderForm.value.evaluationReason,
        type: 0 // 正式评估类型
      }
    })
  } else {
    // 创建评估任务清单执行记录，然后跳转到新的步骤式评估页面
    createListExecution()
  }
}

// 创建评估任务清单执行记录
const createListExecution = async () => {
  try {
    // 确保elderId是数字类型
    if (!elderForm.value.elderId) {
      message.error('请选择老人')
      return
    }

    // 尝试获取第一个模板的API密钥
    let apiKey = ''
    if (selectedList.value.templateIds) {
      const templateIds = selectedList.value.templateIds.split(',')
      if (templateIds.length > 0) {
        try {
          const firstTemplateId = Number(templateIds[0])
          if (!isNaN(firstTemplateId)) {
            const apiKeyRes = await TemplateApi.getTemplateApiKey(firstTemplateId)
            if (apiKeyRes) {
              apiKey = apiKeyRes
            }
          }
        } catch (error) {
          console.error('获取模板API密钥失败:', error)
        }
      }
    }

    // 创建评估任务清单执行记录
    const executionData = {
      listId: selectedList.value.id,
      listName: selectedList.value.name,
      elderId: Number(elderForm.value.elderId), // 确保是数字类型
      elderName: elderForm.value.elderName,
      evaluatorId: userStore.user.id,
      evaluatorName: userStore.user.nickname,
      evaluationReason: elderForm.value.evaluationReason,
      status: 0,
      startTime: new Date().getTime(), // 使用时间戳
      requiredTemplateIds: selectedList.value.templateIds,
      apiKey: apiKey // 设置API密钥
    }

    const result = await ListExecutionApi.createListExecution(executionData)

    // 跳转到步骤式评估页面
    router.push({
      path: '/evaluation/listEval',
      query: {
        id: result,
        fromApplication: 'true'
      }
    })
  } catch (error) {
    console.error('创建评估任务清单执行记录失败:', error)
    message.error('创建评估任务清单执行记录失败')
  }
}

// 不再需要评估对话框相关处理函数

onMounted(() => {
  getElderList()
})
</script>

<style lang="scss" scoped>
.intro-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
  }

  .intro-content {
    line-height: 1.6;
  }
}

.evaluation-tabs-container {
  width: 100%;
  overflow-x: hidden; /* 防止水平滚动 */
  min-width: 1200px; /* 设置最小宽度，确保卡片布局不会因窗口缩小而变形 */

  :deep(.el-tabs__content) {
    overflow: visible; /* 确保标签内容可见 */
  }
}

.mt-20 {
  margin-top: 20px;
}

.text-center {
  text-align: center;
}

.elder-option {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .elder-info {
    color: #909399;
    font-size: 12px;
  }
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .app-container {
    overflow-x: auto; /* 当窗口小于1200px时，允许水平滚动 */
  }

  .evaluation-tabs-container {
    padding: 0 10px;
  }
}
</style>
