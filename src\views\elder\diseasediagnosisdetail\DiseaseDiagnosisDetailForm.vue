<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="诊断ID" prop="diagnosisId">
        <el-input v-model="formData.diagnosisId" placeholder="请输入诊断ID" />
      </el-form-item>
      <el-form-item label="疾病编码" prop="diseaseCode">
        <el-input v-model="formData.diseaseCode" placeholder="请输入疾病编码(ICD-10)" />
      </el-form-item>
      <el-form-item label="疾病名称" prop="diseaseName">
        <el-input v-model="formData.diseaseName" placeholder="请输入疾病名称" />
      </el-form-item>
      <el-form-item label="诊断描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          placeholder="请输入诊断描述"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="是否主要诊断" prop="isMain">
        <el-radio-group v-model="formData.isMain">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { DiseaseDiagnosisDetailApi, DiseaseDiagnosisDetailVO } from '@/api/elder/diseasediagnosisdetail'

/** 疾病诊断详情 表单 */
defineOptions({ name: 'DiseaseDiagnosisDetailForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  diagnosisId: undefined,
  diseaseCode: undefined,
  diseaseName: undefined,
  description: undefined,
  isMain: false,
})
const formRules = reactive({
  diagnosisId: [{ required: true, message: '诊断ID不能为空', trigger: 'blur' }],
  diseaseCode: [{ required: true, message: '疾病编码不能为空', trigger: 'blur' }],
  diseaseName: [{ required: true, message: '疾病名称不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '添加疾病诊断详情' : '修改疾病诊断详情'
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await DiseaseDiagnosisDetailApi.getDiseaseDiagnosisDetail(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DiseaseDiagnosisDetailVO
    if (formType.value === 'create') {
      await DiseaseDiagnosisDetailApi.createDiseaseDiagnosisDetail(data)
      message.success(t('common.createSuccess'))
    } else {
      await DiseaseDiagnosisDetailApi.updateDiseaseDiagnosisDetail(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    diagnosisId: undefined,
    diseaseCode: undefined,
    diseaseName: undefined,
    description: undefined,
    isMain: false,
  }
  formRef.value?.resetFields()
}
</script>
