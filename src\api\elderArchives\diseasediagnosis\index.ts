import request from '@/config/axios'

// 老人疾病诊断 VO
export interface DiseaseDiagnosisVO {
  id: number // 主键ID
  elderId: number // 老人ID
  elderName?: string // 老人姓名
  diagnosisDate: Date // 诊断日期
  doctorName: string // 诊断医生
  hospitalName: string // 诊断机构
  diagnosisType: number // 诊断类型(1-初诊 2-复诊 3-出院诊断)
  diagnosisResult: string // 诊断结果概述
  treatmentPlan: string // 治疗方案
  remark: string // 备注
  createTime?: Date // 创建时间
  updateTime?: Date // 更新时间
  creator?: string // 创建者
}

// 带关联信息的老人疾病诊断 VO
export interface DiseaseDiagnosisWithRelatedVO extends DiseaseDiagnosisVO {
  // 继承了DiseaseDiagnosisVO中的所有字段，包括elderName
}

// 老人疾病诊断 API
export const DiseaseDiagnosisApi = {
  // 查询老人疾病诊断分页
  getDiseaseDiagnosisPage: async (params: any) => {
    return await request.get({ url: `/elderArchives/disease-diagnosis/page`, params })
  },

  // 查询老人疾病诊断详情
  getDiseaseDiagnosis: async (id: number) => {
    return await request.get({ url: `/elderArchives/disease-diagnosis/get?id=` + id })
  },

  // 查询带关联信息的老人疾病诊断详情
  getDiseaseDiagnosisWithRelated: async (id: number) => {
    return await request.get({ url: `/elderArchives/disease-diagnosis/get-with-related?id=` + id })
  },

  // 新增老人疾病诊断
  createDiseaseDiagnosis: async (data: DiseaseDiagnosisVO) => {
    return await request.post({ url: `/elderArchives/disease-diagnosis/create`, data })
  },

  // 修改老人疾病诊断
  updateDiseaseDiagnosis: async (data: DiseaseDiagnosisVO) => {
    return await request.put({ url: `/elderArchives/disease-diagnosis/update`, data })
  },

  // 删除老人疾病诊断
  deleteDiseaseDiagnosis: async (id: number) => {
    return await request.delete({ url: `/elderArchives/disease-diagnosis/delete?id=` + id })
  },

  // 导出老人疾病诊断 Excel
  exportDiseaseDiagnosis: async (params) => {
    return await request.download({ url: `/elderArchives/disease-diagnosis/export-excel`, params })
  },
}
