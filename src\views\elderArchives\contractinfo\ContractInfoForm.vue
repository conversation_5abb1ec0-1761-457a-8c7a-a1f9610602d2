<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="老人" prop="elderId">
        <el-select
          v-model="formData.elderId"
          placeholder="请选择老人"
          clearable
          filterable
          class="!w-full"
        >
          <el-option
            v-for="item in elderProfiles"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="签约员工" prop="employeeIds">
        <el-select
          v-model="formData.employeeIds"
          placeholder="请选择签约员工"
          multiple
          collapse-tags
          collapse-tags-tooltip
          class="!w-full"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="签约日期" prop="contractDate">
        <el-date-picker
          v-model="formData.contractDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择签约日期"
          class="!w-full"
        />
      </el-form-item>
      <el-form-item label="签约类型" prop="contractType">
        <el-select v-model="formData.contractType" placeholder="请选择签约类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_CONTRACT_INFO_CONTRACT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="签约原因" prop="contractReason">
        <el-input v-model="formData.contractReason" placeholder="请输入签约原因" />
      </el-form-item>
      <el-form-item label="签约期限(月)" prop="contractPeriod">
        <el-input v-model="formData.contractPeriod" placeholder="请输入签约期限(月)" />
      </el-form-item>
      <el-form-item label="开始日期" prop="startDate">
        <el-date-picker
          v-model="formData.startDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择开始日期"
          class="!w-full"
        />
      </el-form-item>
      <el-form-item label="结束日期" prop="endDate">
        <el-date-picker
          v-model="formData.endDate"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择结束日期"
          class="!w-full"
        />
      </el-form-item>
      <el-form-item label="签约状态" prop="contractStatus">
        <el-radio-group v-model="formData.contractStatus">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_CONTRACT_INFO_CONTRACT_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="终止原因" prop="terminationReason">
        <el-input v-model="formData.terminationReason" placeholder="请输入终止原因" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ContractInfoApi, ContractInfoVO } from '@/api/elderArchives/contractinfo'
import { ArchivesProfileApi, ArchivesProfileSimpleVO } from '@/api/elderArchives/archivesProfile'
import { getSimpleUserList, UserVO } from '@/api/system/user'

/** 老人签约信息 表单 */
defineOptions({ name: 'ContractInfoForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  elderId: undefined,
  employeeIds: undefined,
  contractDate: undefined,
  contractType: undefined,
  contractReason: undefined,
  contractPeriod: undefined,
  startDate: undefined,
  endDate: undefined,
  contractStatus: undefined,
  terminationReason: undefined,
  remark: undefined,
})
const formRules = reactive({
  elderId: [{ required: true, message: '请选择老人', trigger: 'change' }],
  contractDate: [{ required: true, message: '签约日期不能为空', trigger: 'blur' }],
  contractType: [{ required: true, message: '签约类型不能为空', trigger: 'change' }],
  contractPeriod: [{ required: true, message: '签约期限(月)不能为空', trigger: 'blur' }],
  startDate: [{ required: true, message: '开始日期不能为空', trigger: 'blur' }],
  endDate: [{ required: true, message: '结束日期不能为空', trigger: 'blur' }],
  contractStatus: [{ required: true, message: '签约状态不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
// 老人列表
const elderProfiles = ref<ArchivesProfileSimpleVO[]>([])
// 员工列表
const userList = ref<UserVO[]>([])

/** 获取老人列表 */
const getElderProfiles = async () => {
  try {
    elderProfiles.value = await ArchivesProfileApi.getArchivesProfileSimpleList()
  } catch (error) {
    console.error('获取老人列表失败', error)
  }
}

/** 获取员工列表 */
const getUserList = async () => {
  try {
    userList.value = await getSimpleUserList()
  } catch (error) {
    console.error('获取员工列表失败', error)
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()

  // 获取老人列表和员工列表
  formLoading.value = true
  try {
    await getElderProfiles()
    await getUserList()

    // 修改时，设置数据
    if (id) {
      const data = await ContractInfoApi.getContractInfo(id)
      formData.value = { ...data }

      // 将employeeIds字符串转换为数组格式，以支持多选框显示
      if (formData.value.employeeIds && typeof formData.value.employeeIds === 'string') {
        formData.value.employeeIds = formData.value.employeeIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
      } else {
        formData.value.employeeIds = []
      }

      // 处理日期字段格式转换：LocalDate数组格式 [year, month, day] 转换为字符串格式 "YYYY-MM-DD"
      const convertDateArrayToString = (dateArray: any) => {
        if (Array.isArray(dateArray) && dateArray.length === 3) {
          const [year, month, day] = dateArray
          return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
        }
        return dateArray
      }

      formData.value.contractDate = convertDateArrayToString(formData.value.contractDate)
      formData.value.startDate = convertDateArrayToString(formData.value.startDate)
      formData.value.endDate = convertDateArrayToString(formData.value.endDate)
    }
  } finally {
    formLoading.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = { ...formData.value } as unknown as ContractInfoVO

    // 将employeeIds数组转换为字符串格式
    if (Array.isArray(data.employeeIds)) {
      data.employeeIds = data.employeeIds.join(',')
    }

    if (formType.value === 'create') {
      await ContractInfoApi.createContractInfo(data)
      message.success(t('common.createSuccess'))
    } else {
      await ContractInfoApi.updateContractInfo(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    elderId: undefined,
    employeeIds: [], // 重置为空数组，支持多选框
    contractDate: undefined,
    contractType: undefined,
    contractReason: undefined,
    contractPeriod: undefined,
    startDate: undefined,
    endDate: undefined,
    contractStatus: undefined,
    terminationReason: undefined,
    remark: undefined,
  }
  formRef.value?.resetFields()
}
</script>
