<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="80px"
    >
      <el-form-item label="楼宇" prop="buildingId">
        <el-select
          v-model="queryParams.buildingId"
          placeholder="请选择楼宇"
          clearable
          class="!w-240px"
          @change="handleBuildingChange"
        >
          <el-option
            v-for="building in buildingList"
            :key="building.id"
            :label="building.name"
            :value="building.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="楼层" prop="floor">
        <el-select
          v-model="queryParams.floor"
          placeholder="请选择楼层"
          clearable
          class="!w-240px"
          @change="handleFloorChange"
        >
          <el-option
            v-for="floor in floorList"
            :key="floor"
            :label="floor + '层'"
            :value="floor"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="房间号" prop="roomNumber">
        <el-input
          v-model="queryParams.roomNumber"
          placeholder="请输入房间号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="房间类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择房间类型"
          clearable
          class="!w-240px"
        >
          <el-option label="单人间" :value="1" />
          <el-option label="双人间" :value="2" />
          <el-option label="活动室" :value="3" />
          <el-option label="餐厅" :value="4" />
          <el-option label="医疗室" :value="5" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="楼宇" align="center" prop="buildingName" min-width="100" />
      <el-table-column label="楼层" align="center" prop="floor" min-width="80" />
      <el-table-column label="房间号" align="center" prop="roomNumber" min-width="100" />
      <el-table-column label="房间类型" align="center" prop="type" min-width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.type === 1" type="success">单人间</el-tag>
          <el-tag v-else-if="scope.row.type === 2" type="warning">双人间</el-tag>
          <el-tag v-else-if="scope.row.type === 3" type="info">活动室</el-tag>
          <el-tag v-else-if="scope.row.type === 4" type="danger">餐厅</el-tag>
          <el-tag v-else-if="scope.row.type === 5" type="primary">医疗室</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="床位数量" align="center" prop="bedCount" min-width="100" />
      <el-table-column label="房间容量" align="center" prop="capacity" min-width="100" />
      <el-table-column label="房间状态" align="center" prop="status" min-width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 1" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.status === 2" type="danger">维修中</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="房间设备" align="center" prop="facilities" min-width="150" />
      <el-table-column label="房间描述" align="center" prop="description" min-width="150" />
      <el-table-column label="操作" align="center" width="200">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="handleEdit(scope.row)"
          >
          <!-- <el-button
            link
            type="primary"
            @click="handleEdit(scope.row)"
            v-hasPermi="['institution:room:update']"
          > -->
            <Icon icon="ep:edit" class="mr-5px" /> 编辑
          </el-button>
          <el-button
            link
            type="primary"
            @click="handleView(scope.row)"
          >
          <!-- <el-button
            link
            type="primary"
            @click="handleView(scope.row)"
            v-hasPermi="['institution:room:query']"
          > -->

            <Icon icon="ep:view" class="mr-5px" /> 查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <!-- 表单弹窗：编辑 -->
  <RoomForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { RoomService } from '@/api/institution/room'
import { BuildingService } from '@/api/institution/building'
import RoomForm from './RoomForm.vue'

/** 床位管理 */
defineOptions({ name: 'Bed' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 加载中
const list = ref([]) // 列表数据
const buildingList = ref([]) // 楼宇列表
const floorList = ref([]) // 楼层列表

/** 查询参数 */
const queryParams = ref({
  buildingId: undefined,
  floor: undefined,
  roomNumber: undefined,
  type: undefined
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 获取房间列表
    const data = await RoomService.getRoomList(queryParams.value)
    list.value = data
  } catch (error) {
    console.error('获取房间列表失败:', error)
    message.error('获取房间列表失败')
  } finally {
    loading.value = false
  }
}

/** 获取楼宇列表 */
const getBuildingList = async () => {
  try {
    const data = await BuildingService.getBuildingList({})
    buildingList.value = data
  } catch (error) {
    console.error('获取楼宇列表失败:', error)
    message.error('获取楼宇列表失败')
  }
}

/** 楼宇变更 */
const handleBuildingChange = async (buildingId: number) => {
  queryParams.value.floor = undefined
  if (buildingId) {
    try {
      const building = await BuildingService.getBuilding(buildingId)
      floorList.value = Array.from({ length: building.floors }, (_, i) => i + 1)
    } catch (error) {
      console.error('获取楼层列表失败:', error)
      message.error('获取楼层列表失败')
    }
  } else {
    floorList.value = []
  }
}

/** 楼层变更 */
const handleFloorChange = (floor: number) => {
  // 楼层变更时重新加载房间列表
  getList()
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value = {
    buildingId: undefined,
    floor: undefined,
    roomNumber: undefined,
    type: undefined
  }
  handleQuery()
}

/** 编辑操作 */
const formRef = ref()
const handleEdit = (row: any) => {
  formRef.value.open('update', row.id)
}

/** 查看操作 */
const handleView = (row: any) => {
  formRef.value.open('view', row.id)
}

/** 初始化 **/
onMounted(() => {
  getBuildingList()
  getList()
})
</script>

<style scoped>
.el-tag {
  margin-right: 5px;
}
</style>
