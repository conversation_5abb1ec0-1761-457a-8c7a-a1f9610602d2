<template>
  <div class="evaluation-page">
    <ContentWrap :title="pageTitle">
      <!-- 顶部操作栏 -->
      <div class="action-bar">
        <el-button @click="goBack" icon="ArrowLeft">返回</el-button>
        <div class="action-buttons">
          <el-button
            type="primary"
            :icon="autoSaveEnabled ? 'Select' : ''"
            @click="toggleAutoSave"
          >
            {{ autoSaveEnabled ? '自动保存已开启' : '开启自动保存' }}
          </el-button>
          <el-button
            type="success"
            @click="saveEvaluation"
            :loading="savingDraft"
          >
            保存
          </el-button>
          <el-button
            type="danger"
            @click="showSubmitConfirm"
            :disabled="!canSubmit"
          >
            提交
          </el-button>
        </div>
      </div>

      <!-- 主体内容区 -->
      <el-row :gutter="20" class="main-content">
        <!-- 左侧表单区域 -->
        <el-col :xs="24" :sm="24" :md="16" :lg="18" class="form-container">
          <div class="left-column">
            <el-card shadow="hover" v-loading="formLoading" class="form-card">
              <template #header>
                <div class="form-header">
                  <span>{{ evaluationType === 'template' ? '模板评估' : '清单评估' }}</span>
                  <div v-if="evaluationType === 'list' && templateList.length > 0" class="template-tabs">
                    <el-tabs v-model="activeTemplateId" @tab-change="handleTemplateChange">
                      <el-tab-pane
                        v-for="template in templateList"
                        :key="template.id"
                        :label="template.name"
                        :name="template.id.toString()"
                      />
                    </el-tabs>
                  </div>
                </div>
              </template>

              <!-- 动态表单 -->
              <div class="dynamic-form">
                <div v-if="formRule.length > 0" class="form-wrapper">
                  <FormCreate
                    ref="formRef"
                    v-model="formData"
                    :rule="formRule"
                    :option="formOption"
                    @submit="handleFormSubmit"
                  />
                </div>
                <div v-else class="no-form">
                  <el-empty description="表单加载中或未找到表单配置" />
                </div>
              </div>
            </el-card>

            <!-- 评估师分析区域 -->
            <el-card shadow="hover" class="mt-20 analysis-card">
              <template #header>
                <div class="card-header">
                  <span>评估师分析</span>
                </div>
              </template>
              <div class="evaluator-analysis">
                <el-input
                  v-model="evaluatorAnalysis"
                  type="textarea"
                  :rows="6"
                  placeholder="请输入评估师分析意见..."
                />
              </div>
            </el-card>

            <!-- AI分析区域 -->
            <el-card shadow="hover" class="mt-20 analysis-card">
              <template #header>
                <div class="card-header">
                  <span>AI分析</span>
                  <el-button
                    type="primary"
                    link
                    :loading="generatingAI"
                    @click="generateAIAnalysis"
                  >
                    {{ aiAnalysis ? '重新生成' : '生成AI分析' }}
                  </el-button>
                </div>
              </template>
              <div class="ai-analysis" v-loading="generatingAI">
                <div v-if="aiAnalysis" class="ai-content" v-html="formattedAIAnalysis"></div>
                <div v-else class="no-ai">
                  <el-empty description="暂无AI分析，点击生成AI分析按钮生成" />
                </div>
              </div>
            </el-card>
          </div>
        </el-col>

        <!-- 右侧信息区域 -->
        <el-col :xs="24" :sm="24" :md="8" :lg="6" class="info-container">
          <div class="right-column">
            <!-- 老人信息卡片 -->
            <ElderInfoCard :elder-id="Number(elderId)" class="elder-info-card" />

            <!-- 评估信息卡片 -->
            <el-card shadow="hover" class="mt-20 info-card">
              <template #header>
                <div class="card-header">
                  <span>评估信息</span>
                </div>
              </template>
              <div class="evaluation-info">
                <el-descriptions :column="1" border size="small">
                  <el-descriptions-item label="评估类型">
                    {{ evaluationType === 'template' ? '模板评估' : '清单评估' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="评估对象">
                    {{ evaluationType === 'template' ? templateName : listName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="评估师">
                    {{ evaluatorName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="评估原因">
                    {{ evaluationReason }}
                  </el-descriptions-item>
                  <el-descriptions-item label="评估时间">
                    {{ formatDate(new Date()) }}
                  </el-descriptions-item>
                  <el-descriptions-item v-if="lastSaveTime" label="上次保存">
                    {{ formatDate(lastSaveTime) }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-card>

            <!-- 保存状态卡片 -->
            <el-card v-if="autoSaveEnabled" shadow="hover" class="mt-20 info-card">
              <template #header>
                <div class="card-header">
                  <span>自动保存状态</span>
                </div>
              </template>
              <div class="auto-save-info">
                <p>自动保存间隔: {{ autoSaveInterval / 1000 }}秒</p>
                <p>下次保存: {{ nextSaveTime }}</p>
                <el-progress
                  :percentage="autoSaveProgress"
                  :status="autoSaveStatus"
                  :stroke-width="10"
                />
              </div>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </ContentWrap>

    <!-- 提交确认对话框 -->
    <el-dialog
      v-model="submitDialogVisible"
      title="确认提交评估"
      width="500px"
      destroy-on-close
    >
      <div class="submit-confirm">
        <p>您确定要提交此评估吗？提交后将无法修改。</p>
        <div class="submit-summary">
          <h4>评估摘要</h4>
          <el-descriptions :column="1" border size="small">
            <el-descriptions-item label="评估类型">
              {{ evaluationType === 'template' ? '模板评估' : '清单评估' }}
            </el-descriptions-item>
            <el-descriptions-item label="评估对象">
              {{ evaluationType === 'template' ? templateName : listName }}
            </el-descriptions-item>
            <el-descriptions-item label="老人">
              {{ elderName }}
            </el-descriptions-item>
            <el-descriptions-item label="评估师">
              {{ evaluatorName }}
            </el-descriptions-item>
            <el-descriptions-item label="评估原因">
              {{ evaluationReason }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="submitDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="submitting"
            @click="submitEvaluation"
          >
            确认提交
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { TemplateApi } from '@/api/evaluation/template'
import { ListApi } from '@/api/evaluation/list'
import { ResultApi, ResultVO } from '@/api/evaluation/result'
import { ListExecutionApi, ListExecutionVO } from '@/api/evaluation/listexecution'
import { EvaluationApplicationApi } from '@/api/evaluation/application'
import { formatDate } from '@/utils/formatTime'
import { useMessage } from '@/hooks/web/useMessage'
import { marked } from 'marked'
import FormCreate from '@form-create/element-ui'
import ElderInfoCard from './components/ElderInfoCard.vue'

defineOptions({ name: 'EvaluationPage' })

const route = useRoute()
const router = useRouter()
const message = useMessage()
const userStore = useUserStore()

// 路由参数
const evaluationType = ref(route.query.type as string || 'template')
const templateId = ref(route.query.templateId as string || '')
const templateName = ref(route.query.templateName as string || '')
const listId = ref(route.query.listId as string || '')
const listName = ref(route.query.listName as string || '')
const elderId = ref(route.query.elderId as string || '')
const elderName = ref(route.query.elderName as string || '')
const evaluatorId = ref(route.query.evaluatorId as string || userStore.user.id)
const evaluatorName = ref(route.query.evaluatorName as string || userStore.user.nickname)
const evaluationReason = ref(route.query.evaluationReason as string || '')

// 页面标题
const pageTitle = computed(() => {
  if (evaluationType.value === 'template') {
    return `模板评估 - ${templateName.value}`
  } else {
    return `清单评估 - ${listName.value}`
  }
})

// 表单相关
const formRef = ref(null)
const formData = ref({})
const formRule = ref([])
const formOption = ref({
  submitBtn: { show: false },
  resetBtn: { show: false }
})
const formLoading = ref(false)

// 清单评估相关
const templateList = ref([])
const activeTemplateId = ref('')

// 评估师分析和AI分析
const evaluatorAnalysis = ref('')
const aiAnalysis = ref('')
const generatingAI = ref(false)

// 保存和提交状态
const savingDraft = ref(false)
const submitting = ref(false)
const lastSaveTime = ref(null)
const submitDialogVisible = ref(false)

// 自动保存相关
const autoSaveEnabled = ref(false)
const autoSaveInterval = ref(60000) // 60秒
const autoSaveTimer = ref(null)
const autoSaveLastTime = ref(Date.now())
const autoSaveProgress = ref(0)
const autoSaveStatus = ref('') // '', 'success', 'exception'
const nextSaveTime = ref('计算中...')

// 格式化AI分析内容
const formattedAIAnalysis = computed(() => {
  if (!aiAnalysis.value) return ''
  return marked(aiAnalysis.value)
})

// 判断是否可以提交
const canSubmit = computed(() => {
  // 这里可以添加表单验证逻辑
  return true
})

// 返回上一页
const goBack = () => {
  router.push('/evaluation/application')
}

// 加载模板表单
const loadTemplateForm = async (id) => {
  formLoading.value = true
  try {
    const template = await TemplateApi.getTemplate(Number(id))
    if (template && template.formSchema) {
      try {
        const schema = JSON.parse(template.formSchema)
        formRule.value = schema
      } catch (e) {
        console.error('解析表单结构失败:', e)
        message.error('解析表单结构失败')
        formRule.value = []
      }
    } else {
      formRule.value = []
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    message.error('加载模板失败')
  } finally {
    formLoading.value = false
  }
}

// 加载清单信息
const loadListInfo = async () => {
  formLoading.value = true
  try {
    const list = await ListApi.getList(Number(listId.value))
    if (list && list.templateIds) {
      const ids = list.templateIds.split(',')
      if (ids.length > 0) {
        // 获取模板列表
        const templates = await TemplateApi.getSimpleTemplateList()
        templateList.value = templates.filter(t => ids.includes(t.id.toString()))

        if (templateList.value.length > 0) {
          // 默认选中第一个模板
          activeTemplateId.value = templateList.value[0].id.toString()
          await loadTemplateForm(activeTemplateId.value)
        }
      }
    }
  } catch (error) {
    console.error('加载清单信息失败:', error)
    message.error('加载清单信息失败')
  } finally {
    formLoading.value = false
  }
}

// 处理模板切换
const handleTemplateChange = async (id) => {
  formData.value = {} // 清空表单数据
  await loadTemplateForm(id)
}

// 处理表单提交
const handleFormSubmit = ({ formData }) => {
  console.log('表单数据:', formData)
  // 这里可以添加表单验证逻辑
}

// 切换自动保存
const toggleAutoSave = () => {
  autoSaveEnabled.value = !autoSaveEnabled.value
  if (autoSaveEnabled.value) {
    startAutoSave()
  } else {
    stopAutoSave()
  }
}

// 开始自动保存
const startAutoSave = () => {
  autoSaveLastTime.value = Date.now()
  updateAutoSaveProgress()

  autoSaveTimer.value = setInterval(() => {
    updateAutoSaveProgress()

    // 如果进度达到100%，执行保存
    if (autoSaveProgress.value >= 100) {
      saveEvaluation(true)
      autoSaveLastTime.value = Date.now()
      autoSaveProgress.value = 0
    }
  }, 1000) // 每秒更新进度
}

// 停止自动保存
const stopAutoSave = () => {
  if (autoSaveTimer.value) {
    clearInterval(autoSaveTimer.value)
    autoSaveTimer.value = null
  }
}

// 更新自动保存进度
const updateAutoSaveProgress = () => {
  const elapsed = Date.now() - autoSaveLastTime.value
  autoSaveProgress.value = Math.min(Math.floor((elapsed / autoSaveInterval.value) * 100), 100)

  // 计算下次保存时间
  const remaining = autoSaveInterval.value - elapsed
  if (remaining > 0) {
    const seconds = Math.ceil(remaining / 1000)
    nextSaveTime.value = `${seconds}秒后`
    autoSaveStatus.value = ''
  } else {
    nextSaveTime.value = '即将保存...'
    autoSaveStatus.value = 'success'
  }
}

// 保存评估数据
const saveEvaluation = async (isAutoSave = false) => {
  if (savingDraft.value) return

  savingDraft.value = true
  autoSaveStatus.value = 'success'

  try {
    // 获取表单数据
    let formValues = {}
    if (formRef.value) {
      formValues = formRef.value.formData
    }

    // 构建保存数据
    const saveData: ResultVO = {
      elderId: Number(elderId.value),
      elderName: elderName.value,
      templateId: evaluationType.value === 'template' ? Number(templateId.value) : Number(activeTemplateId.value),
      templateName: evaluationType.value === 'template' ? templateName.value : templateList.value.find(t => t.id.toString() === activeTemplateId.value)?.name || '',
      evaluatorId: Number(evaluatorId.value),
      evaluatorName: evaluatorName.value,
      evaluationReason: evaluationReason.value,
      evaluationTime: new Date(),
      result: JSON.stringify(formValues),
      type: 2, // 暂存类型
      evaluatorAnalysis: evaluatorAnalysis.value,
      aiInputs: '',
      aiAnalysis: aiAnalysis.value
    }

    // 保存数据
    const res = await EvaluationApplicationApi.saveDraft(saveData)

    lastSaveTime.value = new Date()

    if (!isAutoSave) {
      message.success('保存成功')
    }
  } catch (error) {
    console.error('保存失败:', error)
    autoSaveStatus.value = 'exception'
    if (!isAutoSave) {
      message.error('保存失败')
    }
  } finally {
    savingDraft.value = false
  }
}

// 显示提交确认对话框
const showSubmitConfirm = () => {
  submitDialogVisible.value = true
}

// 提交评估
const submitEvaluation = async () => {
  if (submitting.value) return

  submitting.value = true

  try {
    // 获取表单数据
    let formValues = {}
    if (formRef.value) {
      formValues = formRef.value.formData
    }

    if (evaluationType.value === 'template') {
      // 单模板评估提交
      const submitData: ResultVO = {
        elderId: Number(elderId.value),
        elderName: elderName.value,
        templateId: Number(templateId.value),
        templateName: templateName.value,
        evaluatorId: Number(evaluatorId.value),
        evaluatorName: evaluatorName.value,
        evaluationReason: evaluationReason.value,
        evaluationTime: new Date(),
        result: JSON.stringify(formValues),
        type: 0, // 正式评估类型
        evaluatorAnalysis: evaluatorAnalysis.value,
        aiInputs: '',
        aiAnalysis: aiAnalysis.value
      }

      await EvaluationApplicationApi.submitEvaluation(submitData)
    } else {
      // 清单评估提交
      // 这里需要根据实际业务逻辑实现清单评估的提交
      // 可能需要创建多个评估结果，然后关联到清单执行
      message.info('清单评估提交功能待实现')
      submitDialogVisible.value = false
      submitting.value = false
      return
    }

    message.success('提交成功')
    submitDialogVisible.value = false

    // 提交成功后跳转到评估结果页面
    router.push('/evaluation/result')
  } catch (error) {
    console.error('提交失败:', error)
    message.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 生成AI分析
const generateAIAnalysis = async () => {
  if (generatingAI.value) return

  generatingAI.value = true

  try {
    // 模拟AI分析生成
    setTimeout(() => {
      aiAnalysis.value = `# 评估分析报告\n\n## 基本情况\n老人 **${elderName.value}** 进行了${evaluationType.value === 'template' ? templateName.value : listName.value}评估。\n\n## 评估结果\n根据评估数据分析，老人的主要情况如下：\n\n1. 生活自理能力良好\n2. 认知功能正常\n3. 情绪状态稳定\n\n## 建议\n建议针对老人的情况，提供以下服务：\n\n- 定期健康检查\n- 社交活动参与\n- 适当的体育锻炼`
      generatingAI.value = false
    }, 2000)
  } catch (error) {
    console.error('生成AI分析失败:', error)
    message.error('生成AI分析失败')
    generatingAI.value = false
  }
}

// 初始化
onMounted(async () => {
  if (evaluationType.value === 'template') {
    // 加载模板表单
    await loadTemplateForm(templateId.value)
  } else {
    // 加载清单信息
    await loadListInfo()
  }
})

// 组件销毁前清理
onBeforeUnmount(() => {
  stopAutoSave()
})
</script>

<style lang="scss" scoped>
.evaluation-page {
  .action-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .action-buttons {
      display: flex;
      gap: 10px;
    }
  }

  .main-content {
    height: calc(100vh - 180px);

    .form-container {
      height: 100%;
      max-width: 75%; /* 限制左侧容器最大宽度 */

      .left-column {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .form-card {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        max-width: 100%; /* 确保卡片不超出容器 */

        :deep(.el-card__body) {
          flex: 1;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          max-width: 100%; /* 确保卡片内容不超出 */
        }
      }

      .form-header {
        display: flex;
        flex-direction: column;

        .template-tabs {
          margin-top: 10px;
        }
      }

      .dynamic-form {
        flex: 1;
        overflow-y: auto;
        max-width: 100%;
        word-wrap: break-word;
        word-break: break-all;

        /* 确保表单内容不会超出容器宽度 */
        :deep(.el-form) {
          max-width: 100%;
        }

        :deep(.el-form-item) {
          max-width: 100%;

          .el-form-item__label {
            word-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            line-height: 1.4;
          }

          .el-form-item__content {
            max-width: 100%;

            .el-input,
            .el-textarea,
            .el-select,
            .el-radio-group,
            .el-checkbox-group {
              max-width: 100%;
            }

            .el-input__inner,
            .el-textarea__inner {
              word-wrap: break-word;
              word-break: break-all;
            }
          }
        }

        .form-wrapper {
          margin-bottom: 20px;
        }

        .no-form {
          padding: 40px 0;
        }
      }

      .analysis-card {
        max-height: 250px;
        display: flex;
        flex-direction: column;

        :deep(.el-card__body) {
          flex: 1;
          overflow: hidden;
          display: flex;
          flex-direction: column;
        }
      }

      .evaluator-analysis, .ai-analysis {
        flex: 1;
        overflow-y: auto;
        min-height: 150px;

        /* 隐藏滚动条但保留滚动功能 */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Opera */
        }
      }

      .ai-content {
        padding: 10px;
        background-color: #f5f7fa;
        border-radius: 4px;
        max-height: 180px;
        overflow-y: auto;

        /* 隐藏滚动条但保留滚动功能 */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        &::-webkit-scrollbar {
          display: none; /* Chrome, Safari, Opera */
        }

        :deep(h1) {
          font-size: 1.5rem;
          margin-top: 0;
        }

        :deep(h2) {
          font-size: 1.3rem;
        }

        :deep(ul, ol) {
          padding-left: 20px;
        }
      }

      .no-ai {
        padding: 40px 0;
      }
    }

    .info-container {
      height: 100%;
      min-width: 25%; /* 确保右侧容器最小宽度 */
      max-width: 25%; /* 限制右侧容器最大宽度 */

      .right-column {
        height: 100%;
        display: flex;
        flex-direction: column;
        min-width: 100%;
      }

      .elder-info-card {
        flex: 1;
        display: flex;
        flex-direction: column;

        :deep(.el-card__body) {
          flex: 1;
          overflow-y: visible;
        }
      }

      .info-card {
        max-height: 250px;
        display: flex;
        flex-direction: column;

        :deep(.el-card__body) {
          flex: 1;
          overflow-y: visible;
        }
      }

      /* 只有评估师分析和AI分析区域保留滚动条 */
      .analysis-card {
        :deep(.el-card__body) {
          overflow-y: auto;
        }
      }

      .auto-save-info {
        p {
          margin: 5px 0;
        }
      }
    }
  }

  .submit-confirm {
    .submit-summary {
      margin-top: 20px;

      h4 {
        margin-top: 0;
        margin-bottom: 10px;
      }
    }
  }

  .mt-20 {
    margin-top: 20px;
  }
}
</style>
