import request from '@/config/axios'

// 老人联系人信息 VO
export interface ContactInfoVO {
  id: number // 联系人ID
  elderId: number // 关联的老人ID
  name: string // 联系人姓名
  gender: number // 性别
  relationship: number // 与老人的关系
  isEscort: boolean // 是否带领入住
  phone: string // 手机号
  idCard: string // 身份证号
  address: string // 联系地址
  isEmergency: boolean // 是否为紧急联系人
  emergencyPriority: number // 紧急联系人优先级
  remark: string // 备注
}

// 老人联系人信息 API
export const ContactInfoApi = {
  // 查询老人联系人信息分页
  getContactInfoPage: async (params: any) => {
    return await request.get({ url: `/elderArchives/contact-info/page`, params })
  },

  // 查询老人联系人信息详情
  getContactInfo: async (id: number) => {
    return await request.get({ url: `/elderArchives/contact-info/get?id=` + id })
  },

  // 新增老人联系人信息
  createContactInfo: async (data: ContactInfoVO) => {
    return await request.post({ url: `/elderArchives/contact-info/create`, data })
  },

  // 修改老人联系人信息
  updateContactInfo: async (data: ContactInfoVO) => {
    return await request.put({ url: `/elderArchives/contact-info/update`, data })
  },

  // 删除老人联系人信息
  deleteContactInfo: async (id: number) => {
    return await request.delete({ url: `/elderArchives/contact-info/delete?id=` + id })
  },

  // 导出老人联系人信息 Excel
  exportContactInfo: async (params) => {
    return await request.download({ url: `/elderArchives/contact-info/export-excel`, params })
  },
}
