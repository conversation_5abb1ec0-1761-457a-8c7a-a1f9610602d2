import request from '@/config/axios'

// 老人签约信息 VO
export interface ContractInfoVO {
  id: number // 签约信息ID
  elderId: number // 关联的老人ID
  elderName?: string // 老人姓名（前端扩展字段）
  employeeIds: string // 签约员工ID(多选,以逗号分隔)
  employeeNames?: string // 员工姓名（前端扩展字段，多个姓名以逗号分隔）
  contractDate: Date // 签约日期
  contractType: number // 签约类型
  contractReason: string // 签约原因（记录签约目的和服务需求）
  contractPeriod: number // 签约期限(月)
  startDate: Date // 开始日期
  endDate: Date // 结束日期
  contractStatus: number // 签约状态
  terminationReason: string // 终止原因
  remark: string // 备注
}

// 老人签约信息 API
export const ContractInfoApi = {
  // 查询老人签约信息分页
  getContractInfoPage: async (params: any) => {
    return await request.get({ url: `/elderArchives/contract-info/page`, params })
  },

  // 查询老人签约信息详情
  getContractInfo: async (id: number) => {
    return await request.get({ url: `/elderArchives/contract-info/get?id=` + id })
  },

  // 新增老人签约信息
  createContractInfo: async (data: ContractInfoVO) => {
    return await request.post({ url: `/elderArchives/contract-info/create`, data })
  },

  // 修改老人签约信息
  updateContractInfo: async (data: ContractInfoVO) => {
    return await request.put({ url: `/elderArchives/contract-info/update`, data })
  },

  // 删除老人签约信息
  deleteContractInfo: async (id: number) => {
    return await request.delete({ url: `/elderArchives/contract-info/delete?id=` + id })
  },

  // 导出老人签约信息 Excel
  exportContractInfo: async (params) => {
    return await request.download({ url: `/elderArchives/contract-info/export-excel`, params })
  },
}
