<template>
  <Dialog v-model="dialogVisible" title="药品信息录入" width="900px">
    <div v-loading="loading">
      <!-- 用药详情列表 -->
      <div class="mb-4">
        <div class="flex justify-between items-center mb-2">
          <h3 class="text-lg font-bold">用药详情列表</h3>
          <el-button type="primary" @click="showAddForm = true" v-if="!showAddForm">
            <Icon icon="ep:plus" class="mr-5px" /> 新增药品
          </el-button>
        </div>
        
        <el-table :data="detailList" border stripe>
          <el-table-column label="药物名称" prop="medicineName" min-width="120" />
          <el-table-column label="服用方法" prop="usageMethod" min-width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.USAGE_METHOD" :value="scope.row.usageMethod" />
            </template>
          </el-table-column>
          <el-table-column label="用药剂量" prop="dosage" min-width="100" />
          <el-table-column label="用药频率" prop="frequency" min-width="100">
            <template #default="scope">
              <dict-tag :type="DICT_TYPE.MEDICATION_FREQUENCY" :value="scope.row.frequency" />
            </template>
          </el-table-column>
          <el-table-column label="用药注意事项" prop="notes" min-width="150" show-overflow-tooltip />
          <el-table-column label="操作" width="150" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div v-if="detailList.length === 0 && !loading" class="text-center py-4 text-gray-500">
          暂无药品信息，请点击"新增药品"按钮添加
        </div>
      </div>
      
      <!-- 新增/编辑表单 -->
      <div v-if="showAddForm" class="border p-4 rounded-md bg-gray-50">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-bold">{{ isEdit ? '编辑药品信息' : '新增药品信息' }}</h3>
          <el-button type="default" @click="cancelForm">取消</el-button>
        </div>
        
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
        >
          <el-form-item label="药物名称" prop="medicineName">
            <el-input v-model="formData.medicineName" placeholder="请输入药物名称" />
          </el-form-item>
          <el-form-item label="服用方法" prop="usageMethod">
            <el-select v-model="formData.usageMethod" placeholder="请选择服用方法" class="!w-full">
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.USAGE_METHOD)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="用药剂量" prop="dosage">
            <el-input v-model="formData.dosage" placeholder="请输入用药剂量" />
          </el-form-item>
          <el-form-item label="用药频率" prop="frequency">
            <el-select v-model="formData.frequency" placeholder="请选择用药频率" class="!w-full">
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.MEDICATION_FREQUENCY)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="用药注意事项" prop="notes">
            <el-input
              v-model="formData.notes"
              type="textarea"
              :rows="3"
              placeholder="请输入用药注意事项"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm">保存</el-button>
            <el-button @click="cancelForm">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { MedicationDetailApi, MedicationDetailVO } from '@/api/elderArchives/medicationdetail'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

/** 药品信息录入对话框 */
defineOptions({ name: 'MedicationDetailDialog' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const dialogVisible = ref(false) // 弹窗的是否展示
const loading = ref(false) // 加载中
const detailList = ref<MedicationDetailVO[]>([]) // 用药详情列表
const showAddForm = ref(false) // 是否显示新增表单
const isEdit = ref(false) // 是否为编辑模式
const medicationId = ref<number>() // 当前用药记录ID
const formRef = ref() // 表单引用

// 表单数据
const formData = ref<MedicationDetailVO>({
  id: undefined,
  medicationId: undefined,
  medicineName: '',
  usageMethod: '',
  dosage: '',
  frequency: '',
  notes: ''
})

// 表单验证规则
const formRules = reactive({
  medicineName: [{ required: true, message: '药物名称不能为空', trigger: 'blur' }],
  usageMethod: [{ required: true, message: '服用方法不能为空', trigger: 'change' }],
  dosage: [{ required: true, message: '用药剂量不能为空', trigger: 'blur' }],
  frequency: [{ required: true, message: '用药频率不能为空', trigger: 'change' }]
})

/** 打开弹窗 */
const open = async (id: number) => {
  dialogVisible.value = true
  medicationId.value = id
  showAddForm.value = false
  isEdit.value = false
  resetForm()
  await loadDetailList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 加载用药详情列表 */
const loadDetailList = async () => {
  if (!medicationId.value) return
  
  loading.value = true
  try {
    const res = await MedicationDetailApi.getMedicationDetailPage({ 
      medicationId: medicationId.value,
      pageSize: 100 // 设置较大的页面大小以获取所有记录
    })
    detailList.value = res.list
  } catch (error) {
    console.error('获取用药详情列表失败', error)
    message.error('获取用药详情列表失败')
  } finally {
    loading.value = false
  }
}

/** 提交表单 */
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  
  // 设置用药记录ID
  formData.value.medicationId = medicationId.value
  
  loading.value = true
  try {
    if (isEdit.value && formData.value.id) {
      // 编辑模式
      await MedicationDetailApi.updateMedicationDetail(formData.value)
      message.success('修改成功')
    } else {
      // 新增模式
      await MedicationDetailApi.createMedicationDetail(formData.value)
      message.success('新增成功')
    }
    
    // 重新加载列表
    await loadDetailList()
    // 重置表单
    resetForm()
    // 隐藏表单
    showAddForm.value = false
  } catch (error) {
    console.error('保存用药详情失败', error)
    message.error('保存失败，请重试')
  } finally {
    loading.value = false
  }
}

/** 取消表单 */
const cancelForm = () => {
  showAddForm.value = false
  resetForm()
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    medicationId: medicationId.value,
    medicineName: '',
    usageMethod: '',
    dosage: '',
    frequency: '',
    notes: ''
  }
  formRef.value?.resetFields()
}

/** 编辑用药详情 */
const handleEdit = (row: MedicationDetailVO) => {
  isEdit.value = true
  showAddForm.value = true
  formData.value = { ...row }
}

/** 删除用药详情 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await MedicationDetailApi.deleteMedicationDetail(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await loadDetailList()
  } catch {}
}
</script>
