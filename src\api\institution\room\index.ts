import request from '@/config/axios'

export interface RoomVO {
  id?: number
  buildingId?: number
  buildingName: string
  floor?: number
  roomNumber: string
  type?: number
  bedCount: number
  capacity: number
  status: number
  facilities: string
  description: string
  createTime: Date
}

export interface RoomPageReqVO extends PageParam {
  buildingId?: number
  floor?: number
  roomNumber?: string
  type?: number
  status?: number
}

// 查询房间列表
export const getRoomList = (params: RoomPageReqVO) => {
  return request.get({ url: '/institution/room/list', params })
}

// 查询房间详情
export const getRoom = (id: number) => {
  return request.get({ url: '/institution/room/get?id=' + id })
}

// 新增房间
export const createRoom = (data: RoomVO) => {
  return request.post({ url: '/institution/room/create', data })
}

// 修改房间
export const updateRoom = (data: RoomVO) => {
  return request.put({ url: '/institution/room/update', data })
}

// 删除房间
export const deleteRoom = (id: number) => {
  return request.delete({ url: '/institution/room/delete?id=' + id })
}

// 导出房间 Excel
export const exportRoom = (params: RoomPageReqVO) => {
  return request.download({ url: '/institution/room/export-excel', params })
}

// 导出所有API
export const RoomService = {
  getRoomList,
  getRoom,
  createRoom,
  updateRoom,
  deleteRoom,
  exportRoom
} 