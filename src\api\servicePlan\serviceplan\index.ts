import request from '@/config/axios'

// 服务计划 VO
export interface ServicePlanVO {
  id: number // 自增主键
  elderId: number // 关联老人表
  planName: string // 计划名称（如 术后康复计划）
  status: number // 状态：1-草稿 2-生效 3-终止
  startDate: Date // 计划开始日期
  endDate: Date // 计划结束日期
}

// 服务计划精简信息 VO
export interface ServicePlanSimpleVO {
  id: number // 自增主键
  planName: string // 计划名称
}

// 服务计划 API
export const ServicePlanApi = {
  // 查询服务计划分页
  getServicePlanPage: async (params: any) => {
    return await request.get({ url: `/servicePlan/service-plan/page`, params })
  },

  // 查询服务计划详情
  getServicePlan: async (id: number) => {
    return await request.get({ url: `/servicePlan/service-plan/get?id=` + id })
  },

  // 获取服务计划精简信息列表
  getServicePlanSimpleList: async () => {
    return await request.get<ServicePlanSimpleVO[]>({ 
      url: `/servicePlan/service-plan/list-all-simple` 
    })
  },

  // 新增服务计划
  createServicePlan: async (data: ServicePlanVO) => {
    return await request.post({ url: `/servicePlan/service-plan/create`, data })
  },

  // 修改服务计划
  updateServicePlan: async (data: ServicePlanVO) => {
    return await request.put({ url: `/servicePlan/service-plan/update`, data })
  },

  // 删除服务计划
  deleteServicePlan: async (id: number) => {
    return await request.delete({ url: `/servicePlan/service-plan/delete?id=` + id })
  },

  // 导出服务计划 Excel
  exportServicePlan: async (params) => {
    return await request.download({ url: `/servicePlan/service-plan/export-excel`, params })
  },

  //word导出
  exportWord: async (params) => {
    return await request.download({ url: `/servicePlan/service-plan/export-word`, params })
  },
  // 复制服务计划
  copyServicePlan: async (data: ServicePlanVO, copyTasks: boolean) => {
    return await request.post({ 
      url: `/servicePlan/service-plan/copy`, 
      params: { copyTasks },
      data 
    })
  },

  // 批量复制服务计划
  batchCopyServicePlan: async (data: (ServicePlanVO & { copyTasks: boolean })[]) => {
    return await request.post({ 
      url: `/servicePlan/service-plan/batch-copy`, 
      data 
    })
  },

}
