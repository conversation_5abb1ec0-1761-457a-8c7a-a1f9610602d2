<template>
  <ContentWrap>
    <el-card class="box-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>机构信息</span>
          <el-button
            type="primary"
            @click="handleEdit"
            v-hasPermi="['institution:info:update']"
          >
            <Icon icon="ep:edit" class="mr-5px" /> 编辑
          </el-button>
        </div>
      </template>
      
      <el-descriptions v-if="detailData" :column="2" border>
        <el-descriptions-item label="机构名称">{{ detailData.name || '-' }}</el-descriptions-item>
        <el-descriptions-item label="机构简称">{{ detailData.shortName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="机构类型">
          <DictTag :type="'organization_type'" :value="detailData.type" />
        </el-descriptions-item>
        <el-descriptions-item label="机构等级">
          <DictTag :type="'organization_level'" :value="detailData.level" />
        </el-descriptions-item>
        <el-descriptions-item label="成立时间">
          {{ detailData.establishDate ? formatDate(detailData.establishDate) : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="机构logo" :span="2">
          <el-image v-if="detailData.logo" :src="detailData.logo" style="max-height: 100px;" :preview-src-list="[detailData.logo]" />
          <span v-else>暂无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="营业执照号码">{{ detailData.businessLicense || '-' }}</el-descriptions-item>
        <el-descriptions-item label="营业执照图片" :span="2">
          <el-image v-if="detailData.businessLicenseImg" :src="detailData.businessLicenseImg" style="max-height: 150px;" :preview-src-list="[detailData.businessLicenseImg]" />
          <span v-else>暂无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="法人代表">{{ detailData.legalPerson || '-' }}</el-descriptions-item>
        <el-descriptions-item label="法人联系方式">{{ detailData.legalPersonContact || '-' }}</el-descriptions-item>
        <el-descriptions-item label="床位数量">{{ detailData.bedCount || '-' }}</el-descriptions-item>
        <el-descriptions-item label="占地面积(平方米)">{{ detailData.areaSize || '-' }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ detailData.contactPhone || '-' }}</el-descriptions-item>
        <el-descriptions-item label="紧急联系电话">{{ detailData.emergencyPhone || '-' }}</el-descriptions-item>
        <el-descriptions-item label="电子邮箱">{{ detailData.email || '-' }}</el-descriptions-item>
        <el-descriptions-item label="官方网站">{{ detailData.website || '-' }}</el-descriptions-item>
        <el-descriptions-item label="详细地址" :span="2">{{ detailData.address || '-' }}</el-descriptions-item>
        <el-descriptions-item label="机构简介" :span="2">{{ detailData.introduction || '-' }}</el-descriptions-item>
        <el-descriptions-item label="机构特色" :span="2">{{ detailData.features || '-' }}</el-descriptions-item>
        <el-descriptions-item label="机构文化" :span="2">{{ detailData.culture || '-' }}</el-descriptions-item>
        <el-descriptions-item label="机构荣誉" :span="2">{{ detailData.honors || '-' }}</el-descriptions-item>
      </el-descriptions>
      <el-empty v-else description="暂无数据" />
    </el-card>
  </ContentWrap>

  <!-- 表单弹窗：编辑 -->
  <InfoForm ref="formRef" @success="getDetail" />
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import { InfoApi, InfoVO } from '@/api/institution/info'
import InfoForm from './InfoForm.vue'
import { DictTag } from '@/components/DictTag'
import { getTenantId } from '@/utils/auth'
import './index.css'

/** 机构信息 */
defineOptions({ name: 'Info' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 加载中
const detailData = ref<InfoVO | null>(null) // 详情数据

/** 获取详情 */
const getDetail = async () => {
  loading.value = true
  try {
    // 获取当前租户ID
    const tenantId = getTenantId()
    if (!tenantId) {
      message.error('获取租户信息失败')
      return
    }
    // 获取当前租户的机构信息
    const data = await InfoApi.getInfo(tenantId)
    detailData.value = data
  } catch (error) {
    console.error('获取机构信息失败:', error)
    message.error('获取机构信息失败')
  } finally {
    loading.value = false
  }
}

/** 编辑操作 */
const formRef = ref()
const handleEdit = () => {
  if (!detailData.value?.id) {
    message.error('机构信息不存在')
    return
  }
  formRef.value.open('update', detailData.value.id)
}

/** 初始化 **/
onMounted(() => {
  getDetail()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>