<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="关联的老人ID" prop="elderId">
        <el-input v-model="formData.elderId" placeholder="请输入关联的老人ID" />
      </el-form-item>
      <el-form-item label="联系人姓名" prop="name">
        <el-input v-model="formData.name" placeholder="请输入联系人姓名" />
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-radio-group v-model="formData.gender">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="与老人的关系" prop="relationship">
        <el-select v-model="formData.relationship" placeholder="请选择与老人的关系">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_CONTACT_RELATIONSHIP)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否带领入住" prop="isEscort">
        <el-radio-group v-model="formData.isEscort">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="formData.phone" placeholder="请输入手机号" />
      </el-form-item>
      <el-form-item label="身份证号" prop="idCard">
        <el-input v-model="formData.idCard" placeholder="请输入身份证号" />
      </el-form-item>
      <el-form-item label="联系地址" prop="address">
        <el-input v-model="formData.address" placeholder="请输入联系地址" />
      </el-form-item>
      <el-form-item label="是否为紧急联系人" prop="isEmergency">
        <el-radio-group v-model="formData.isEmergency">
          <el-radio :label="true">紧急联系人</el-radio>
          <el-radio :label="false">非紧急联系人</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="紧急联系人优先级" prop="emergencyPriority">
        <el-select v-model="formData.emergencyPriority" placeholder="请选择紧急联系人优先级">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_CONTACT_EMERGENCY_PRIORITY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ContactInfoApi, ContactInfoVO } from '@/api/elderArchives/contactinfo'

/** 老人联系人信息 表单 */
defineOptions({ name: 'ContactInfoForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  elderId: undefined,
  name: undefined,
  gender: undefined,
  relationship: undefined,
  isEscort: undefined,
  phone: undefined,
  idCard: undefined,
  address: undefined,
  isEmergency: undefined,
  emergencyPriority: undefined,
  remark: undefined,
})
const formRules = reactive({
  elderId: [{ required: true, message: '关联的老人ID不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '联系人姓名不能为空', trigger: 'blur' }],
  gender: [{ required: true, message: '性别不能为空', trigger: 'blur' }],
  relationship: [{ required: true, message: '与老人的关系不能为空', trigger: 'change' }],
  isEscort: [{ required: true, message: '是否带领入住不能为空', trigger: 'blur' }],
  phone: [{ required: true, message: '手机号不能为空', trigger: 'blur' }],
  isEmergency: [{ required: true, message: '是否为紧急联系人不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ContactInfoApi.getContactInfo(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ContactInfoVO
    if (formType.value === 'create') {
      await ContactInfoApi.createContactInfo(data)
      message.success(t('common.createSuccess'))
    } else {
      await ContactInfoApi.updateContactInfo(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    elderId: undefined,
    name: undefined,
    gender: undefined,
    relationship: undefined,
    isEscort: undefined,
    phone: undefined,
    idCard: undefined,
    address: undefined,
    isEmergency: undefined,
    emergencyPriority: undefined,
    remark: undefined,
  }
  formRef.value?.resetFields()
}
</script>
