<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1440px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="service-plan-form form-scaled"
      v-loading="formLoading"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="关联老人" prop="elderId">
            <el-select
              v-model="formData.elderId"
              placeholder="请选择关联老人"
              clearable
              filterable
            >
              <el-option
                v-for="elder in elderList"
                :key="elder.id"
                :label="elder.name"
                :value="elder.id"
              >
                <span>{{ elder.name }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="计划分类" prop="categoryId">
            <el-tree-select
              v-model="formData.categoryId"
              :data="categoryTree"
              :props="categoryProps"
              placeholder="请选择计划分类"
              clearable
              @update:model-value="handleCategoryChange"
            />
          </el-form-item>
          <el-form-item label="计划名称" prop="planName">
            <el-input v-model="formData.planName" placeholder="请输入计划名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12" class="divider-column">
          <div class="divider"></div>
          <el-form-item label="计划日期" prop="dateRange">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              value-format="YYYY-MM-DD"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleDateRangeChange"
              :disabledDate="disabledDate"
              class="!w-full"
            />
          </el-form-item>
          <el-form-item label="关联评估结果">
            <el-select
              v-model="selectedEvaluationIds"
              placeholder="请选择评估结果"
              multiple
              clearable
              filterable
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="2"
              class="!w-full"
            >
              <el-option
                v-for="result in availableEvaluationResults"
                :key="result.id"
                :label="(result as any).templateName || result.label"
                :value="result.id"
              >
                <div class="evaluation-option">
                  <div class="option-title">{{ (result as any).templateName || result.label }}</div>
                  <div class="option-time">{{ formatEvaluationTime((result as any).createTime) }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item v-if="formType === 'update'" label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.SERVICE_PLAN_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="计划描述" prop="description">
        <MarkdownEditor
          v-model="formData.description"
          :ai-button-disabled="aiButtonDisabled"
          :ai-reasoning="aiReasoning"
          :is-thinking="isThinking"
          @generate="generateDescription"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="large" @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button size="large" @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ServicePlanApi, ServicePlanVO } from '@/api/servicePlan/serviceplan'
import { ResultApi ,ResultTemplateVO} from '@/api/evaluation/result'
import { PlanEvaluationRelApi } from '@/api/servicePlan/planevaluationrel'
import { ref, reactive, computed, watch } from 'vue'
import MarkdownEditor from '@/components/MarkdownEditor/index.vue'
import { ArchivesProfileApi } from '@/api/elderArchives/archivesProfile'
import { CategoryApi } from '@/api/servicePlan/serviceplancategory'
import { servicePlanCategoryRelApi } from '@/api/servicePlan/serviceplancategoryrel'
import dayjs from 'dayjs'

/** 服务计划 表单 */
defineOptions({ name: 'ServicePlanForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改

// 定义表单数据接口
interface FormData {
  id?: number
  elderId?: number
  categoryId?: number
  planName?: string
  status?: number
  startDate?: string
  endDate?: string
  description?: string
  categoryName?: string
}

// 定义老人列表接口
interface Elder {
  id: number
  name: string
}

// 初始化表单数据

const formData = ref<FormData>({
  id: undefined,
  elderId: undefined,
  categoryId: undefined,
  planName: undefined,
  status: 1, // 默认状态为草稿
  startDate: undefined,
  endDate: undefined,
  description: '', // 确保初始化为一个空字符串
  categoryName: undefined
})


const formRules = reactive({
  elderId: [{ required: true, message: '关联老人不能为空', trigger: 'blur' }],
  categoryId: [{ required: true, message: '计划分类不能为空', trigger: 'blur' }],
  planName: [{ required: true, message: '计划名称不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  dateRange: [
    { 
      required: true, 
      message: '计划日期不能为空', 
      trigger: 'change',
      validator: (rule: any, value: any, callback: any) => {
        if (!formData.value.startDate || !formData.value.endDate) {
          callback(new Error('请选择计划日期'))
          return
        }
        
        const startDate = dayjs(formData.value.startDate)
        const endDate = dayjs(formData.value.endDate)
        const monthsDiff = endDate.diff(startDate, 'month', true)

        if (monthsDiff > 6) {
          callback(new Error('计划时长不能超过6个月'))
          return
        }

        callback()
      }
    }
  ]
})
const formRef = ref() // 表单 Ref

// 老人列表
const elderList = ref<Elder[]>([])

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 评估结果
const evaluationResults = ref<any[]>([])
// 可选择的评估结果列表
const availableEvaluationResults = ref<any[]>([])
// 选中的评估结果ID列表
const selectedEvaluationIds = ref<number[]>([])

const aiReasoning = ref<string>(''); // 新增变量记录 AI 思考过程的内容
const activeCollapse = ref(['1']); // 控制折叠面板的展开状态
const isThinking = ref(false); // 控制是否正在思考

// 分类树数据
const categoryTree = ref<any[]>([])

// 分类树配置
const categoryProps = {
  children: 'children',
  label: 'name',
  value: 'id',
  checkStrictly: true,
}

// 添加在 script setup 的开始部分
const parseMultiJson = (jsonStr: string) => {
  const jsonArr: any[] = []  // 明确指定数组类型为 any[]
  const lines = jsonStr.split('\n')

  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const content = line.substring(6) // 去掉 "data: " 前缀
      if (content === '[DONE]') {
        continue // 跳过结束标记
      }
      try {
        const jsonData = JSON.parse(content)
        jsonArr.push(jsonData)
      } catch (error) {
        console.error('JSON 解析失败:', line)
        console.error('错误信息:', error)
      }
    }
  }

  return jsonArr
}

// 添加 AbortController 相关变量
const abortController = ref<AbortController | null>(null)

// 格式化评估时间
const formatEvaluationTime = (timestamp: number) => {
  if (!timestamp) return ''
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm')
}

// 获取评估结果 - 使用原来的方法
const getEvaluationResults = async (categoryId: number, elderId: number) => {
  try {
    const results = await servicePlanCategoryRelApi.getActiveList(categoryId, elderId)
    availableEvaluationResults.value = results || []
  } catch (error) {
    console.error('获取评估结果失败:', error)
    message.error('获取评估结果失败')
    availableEvaluationResults.value = []
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number, elders?: Elder[]) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  
  // 清空 AI 思考内容
  aiReasoning.value = ''; // 清空思考过程内容
  // 清空评估结果
  evaluationResults.value = [];
  selectedEvaluationIds.value = [];
  availableEvaluationResults.value = [];

  // 设置老人列表
  if (elders) {
    elderList.value = elders
  }

  // 获取分类树数据
  try {
    const treeData = await CategoryApi.generateSimpleTree()
    categoryTree.value = treeData || []
  } catch (error) {
    console.error('获取分类树失败:', error)
    message.error('获取分类树失败')
  }

  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const data = await ServicePlanApi.getServicePlan(id)
      formData.value = {
        ...data,
        description: data.description || ''
      }
      // 设置日期范围
      if (data.startDate && data.endDate) {
        dateRange.value = [data.startDate, data.endDate]
      }

      // 获取关联的评估结果
      const resultIds = await PlanEvaluationRelApi.getEvaluationResultIds(id)
      if (resultIds && resultIds.length > 0) {
        // 获取评估结果详情
        const results = await ResultApi.getAiInputsByIds(resultIds)
        evaluationResults.value = results || []
        // 设置选中的评估结果ID
        selectedEvaluationIds.value = resultIds
      }

      // 如果有老人ID和分类ID，获取评估结果
      if (data.elderId && data.categoryId) {
        await getEvaluationResults(data.categoryId, data.elderId)
      }

    } catch (error) {
      console.error('获取服务计划数据失败:', error)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验日期
  if (!formData.value.startDate || !formData.value.endDate) {
    message.error('请选择计划日期')
    return
  }
  
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ServicePlanVO
    let planId: number
    
    if (formType.value === 'create') {
      // 创建服务计划
      planId = await ServicePlanApi.createServicePlan(data)
      message.success(t('common.createSuccess'))
      
      // 保存关联的评估结果
      if (selectedEvaluationIds.value && selectedEvaluationIds.value.length > 0) {
        await PlanEvaluationRelApi.createPlanEvaluationRel({
          servicePlanId: planId,
          evaluationResultIds: selectedEvaluationIds.value
        })
      }
    } else {
      // 更新服务计划
      await ServicePlanApi.updateServicePlan(data)
      planId = data.id!
      message.success(t('common.updateSuccess'))
      
      // 更新关联的评估结果
      if (selectedEvaluationIds.value && selectedEvaluationIds.value.length > 0) {
        await PlanEvaluationRelApi.updatePlanEvaluationRel({
          servicePlanId: planId,
          evaluationResultIds: selectedEvaluationIds.value
        })
      }
    }
    
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } catch (error) {
    console.error('保存服务计划失败:', error)
    message.error('保存失败，请重试')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    elderId: undefined,
    categoryId: undefined,
    planName: undefined,
    status: 1,
    startDate: undefined,
    endDate: undefined,
    description: '',
    categoryName: undefined
  }
  dateRange.value = null
  evaluationResults.value = [] // 清空评估结果
  selectedEvaluationIds.value = [] // 清空选中的评估结果
  availableEvaluationResults.value = [] // 清空可选择的评估结果
  formRef.value?.resetFields()
}

// 日期禁用函数
const disabledDate = (time: Date) => {
  // 禁用今天之前的日期
  if (time.getTime() < dayjs().startOf('day').valueOf()) {
    return true
  }
  
  // 如果已选择开始日期，限制结束日期不能超过开始日期6个月
  if (dateRange.value?.[0] && !dateRange.value[1]) {
    const startDate = dayjs(dateRange.value[0])
    const maxEndDate = startDate.add(6, 'month')
    return time.getTime() > maxEndDate.valueOf()
  }
  
  return false
}

// 处理日期范围变化
const handleDateRangeChange = (val: [string, string] | null) => {
  if (!val) {
    formData.value.startDate = undefined
    formData.value.endDate = undefined
    return
  }

  const [start, end] = val
  
  // 检查日期范围是否超过6个月
  const startDate = dayjs(start)
  const endDate = dayjs(end)
  const monthsDiff = endDate.diff(startDate, 'month', true)

  if (monthsDiff > 6) {
    message.warning('计划时长不能超过6个月')
    dateRange.value = null
    formData.value.startDate = undefined
    formData.value.endDate = undefined
    return
  }

  formData.value.startDate = start
  formData.value.endDate = end
}

//直接生成服务计划描述
const generateDescription = async () => {
  if (!formData.value.elderId) {
    message.error('请先选择关联老人')
    return
  }
  
  // 创建新的 AbortController
  abortController.value = new AbortController()
  
  // 获取分类详情以获取 dify_key
  let difyKey = ''
  if (formData.value.categoryId) {
    try {
      const categoryDetail = await CategoryApi.getCategory(formData.value.categoryId)
      difyKey = categoryDetail.difyKey // 从分类详情中获取 dify_key
      if (!difyKey) {
        message.error('该分类未配置 AI 接口密钥')
        return
      }
    } catch (error) {
      console.error('获取分类详情失败:', error)
      message.error('获取分类详情失败')
      return
    }
  } else {
    message.error('请先选择计划分类')
    return
  }

  // 获取老人信息
  const elderInfo = await ArchivesProfileApi.getArchivesProfile(formData.value.elderId)
  // 获取老人姓名和年龄
  const elderName = elderInfo.name
  const elderAge = new Date().getFullYear() - new Date(elderInfo.birthDate).getFullYear()
  // 获取关联评估结果
  let evaluationInputs = ''
  if (selectedEvaluationIds.value.length > 0) {
    const evaluationResults = await ResultApi.getAiInputsByIds(selectedEvaluationIds.value)
    evaluationInputs = evaluationResults
      .map(result => `${result.label || (result as any).templateName}：\n${result.aiInputs}`)
      .join('\n\n')
  }
  // 使用临时存储的分类名称
  const categoryName = formData.value.categoryName || '未知分类'
  
  // 拼接老人信息和评估结果aiInputs
  const aiInputs = `请生成服务计划的描述，
老人信息：
- 姓名：${elderName}
- 年龄：${elderAge}岁
计划分类：${categoryName}

${selectedEvaluationIds.value.length > 0 ? `评估结果：\n${evaluationInputs}` : '暂无关联评估结果'}`

  const options_ai = {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${difyKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      inputs: {},
      query: aiInputs,
      response_mode: 'streaming',
      conversation_id: '',
      user: 'user123'
    }),
    signal: abortController.value.signal
  }

  try {
    isThinking.value = true;
    activeCollapse.value = ['1'];
    formData.value.description = '';
    aiReasoning.value = '';
    
    const response = await fetch('http://*************/v1/chat-messages', options_ai);
    if (!response.body) throw new Error('Response body is null');

    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    let isDone = false;
    let buffer = '';
    
    // 完整响应内容
    let currentContent = '';
    
    // 处理SSE格式的流数据
    function parseSSE(data: string) {
      const lines = data.split('\n').filter((line) => line.trim() !== '')
      return lines
        .map((line) => {
          if (line.startsWith('data: ')) {
            try {
              return JSON.parse(line.slice(6))
            } catch (error) {
              console.error('解析 SSE 数据失败:', line, error)
              return null
            }
          }
          return null
        })
        .filter(Boolean)
    }
    
    while (!isDone) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });
      
      buffer += chunk;
      const chunks = buffer.split('\n\n');
      
      chunks.slice(0, -1).forEach((chunk) => {
        const events = parseSSE(chunk);
        
        events.forEach((data: any) => {
          if (data.event === 'message' && data.answer) {
            
            // 累积完整内容
            currentContent += data.answer;
            
            // 检查是否包含完整的思考标签对
            if (currentContent.includes('</think>')) {
              const parts = currentContent.split('</think>');
              if (parts.length >= 2) {
                // 提取思考内容
                aiReasoning.value = parts[0].replace(/<think>/g, '').trim();
                
                // 提取回答内容
                formData.value.description = parts[1].trim();
              }
            } else {
              // 暂时将所有内容视为思考内容
              aiReasoning.value = currentContent.replace(/<think>/g, '').trim();
            }
          }
        });
      });
      
      buffer = chunks[chunks.length - 1];
    }
    
    // 最终处理
    if (currentContent.includes('</think>')) {
      const parts = currentContent.split('</think>');
      if (parts.length >= 2) {
        // 提取思考内容
        aiReasoning.value = parts[0].replace(/<think>/g, '').trim();
        
        // 提取回答内容
        formData.value.description = parts[1].trim();
      }
    } else {
      // 如果没有结束标签，将所有内容视为思考内容
      aiReasoning.value = currentContent.replace(/<think>/g, '').trim();
      formData.value.description = ''; // 清空回答内容
    }
    
    
  } catch (error: any) {
    // 区分是否为主动取消的错误
    if (error.name === 'AbortError') {
      console.log('AI 请求已取消')
    } else {
      console.error('获取 AI 描述失败:', error)
      message.error('获取 AI 描述失败')
    }
  } finally {
    isThinking.value = false
    abortController.value = null
  }
};

// 计算 AI 按钮是否禁用
const aiButtonDisabled = computed(() => {
  return !formData.value.elderId || !formData.value.categoryId
})

// 处理分类变化
const handleCategoryChange = (categoryId: number | null) => {
  if (!categoryId) {
    formData.value.categoryId = undefined
    formData.value.categoryName = undefined
    return
  }
  
  // 查找分类名称
  const findCategory = (nodes: any[]): any => {
    for (const node of nodes) {
      if (node.id === categoryId) {
        return node
      }
      if (node.children) {
        const found = findCategory(node.children)
        if (found) return found
      }
    }
    return null
  }

  const category = findCategory(categoryTree.value)
  formData.value.categoryId = categoryId
  formData.value.categoryName = category?.name
}

// 监听弹窗关闭
watch(() => dialogVisible.value, (visible) => {
  if (!visible && abortController.value) {
    // 终止正在进行的 AI 请求
    abortController.value.abort()
    abortController.value = null
    isThinking.value = false
  }
})

// 监听老人和分类的变化，获取评估结果
watch(
  [
    () => formData.value.elderId,
    () => formData.value.categoryId
  ],
  async ([newElderId, newCategoryId]) => {
    // 清空选中的评估结果
    selectedEvaluationIds.value = []
    availableEvaluationResults.value = []

    // 如果老人ID和分类ID都存在，则查询评估结果
    if (newElderId && newCategoryId) {
      await getEvaluationResults(newCategoryId, newElderId)
    }
  },
  {
    immediate: false
  }
)

</script>

<style lang="scss" scoped>
/* 表单整体放大20% */
.form-scaled {
  transform: scale(1.2);
  transform-origin: top left;
  margin-bottom: 20px;
}

.service-plan-form {
  width: 100%;

  :deep(.el-form-item__content) {
    width: calc(100% - 120px); // 减去 label 的宽度 (120px)
  }
}

/* 表单元素字体大小调整 */
.form-scaled :deep(.el-form-item__label) {
  font-size: 16.8px; /* 14px * 1.2 */
}

.form-scaled :deep(.el-input__inner),
.form-scaled :deep(.el-select .el-input__inner),
.form-scaled :deep(.el-textarea__inner) {
  font-size: 16.8px; /* 14px * 1.2 */
}

.form-scaled :deep(.el-button) {
  font-size: 16.8px; /* 14px * 1.2 */
}

.form-scaled :deep(.el-radio__label) {
  font-size: 16.8px; /* 14px * 1.2 */
}

.ai-content {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  
  p {
    margin: 0;
    font-size: 14px;
    color: #606266;
  }
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select-dropdown__item) {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 添加样式以确保按钮在标签下方 */
.el-form-item {
  margin-bottom: 20px; /* 调整表单项之间的间距 */
}

.divider-column {
  position: relative;
  padding-left: 20px;
}

.divider {
  border-left: 1px solid #dcdfe6;
  height: 100%;
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 0;
}

.ai-reasoning-collapse {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;

  :deep(.el-collapse-item__header) {
    font-size: 14px;
    color: #409eff;
    background-color: #f5f7fa;
    padding: 8px 15px;
    height: 40px;
    line-height: 24px;
    
    .collapse-title {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 0 8px;
    }
  }
}

.ai-reasoning-content {
  padding: 12px 15px;
  background-color: #fafafa;
  max-height: 200px;
  overflow-y: auto;

  .reasoning-text {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.6;
    color: #606266;
    white-space: pre-wrap;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: var(--el-fill-color-lighter);
    border-radius: 3px;
  }
}

.description-actions {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 8px;
  
  .ai-button {
    display: inline-flex;
    align-items: center;
    height: 40px;
    
    .el-icon {
      margin-right: 4px;
    }
  }
}

.markdown-editor {
  display: flex;
  gap: 20px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  background-color: var(--el-bg-color);
  height: 250px;
  margin-top: 16px;
  width: 100%;

  .editor-container {
    flex: 1;
    width: calc(50% - 10px);
    display: flex;
    
    :deep(.el-textarea__inner) {
      font-family: monospace;
      line-height: 1.6;
      resize: none;
      border: none;
      border-radius: 0;
      padding: 16px;
      flex: 1;
      height: 100%;
      width: 100%;
      min-height: unset;
    }
  }

  .preview-container {
    flex: 1;
    width: calc(50% - 10px);
    border-left: 1px solid var(--el-border-color);
    height: 100%;
    display: flex;
    flex-direction: column;

    .markdown-preview {
      height: 100%;
      overflow-y: auto;
      width: 100%;
      padding: 16px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: var(--el-border-color);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: var(--el-fill-color-lighter);
        border-radius: 3px;
      }

      :deep(h1, h2, h3, h4, h5, h6) {
        margin-top: 24px;
        margin-bottom: 16px;
        font-weight: 600;
        line-height: 1.25;
      }

      :deep(p) {
        margin-bottom: 16px;
        line-height: 1.5;
      }

      :deep(ul, ol) {
        padding-left: 2em;
        margin-bottom: 16px;
      }

      :deep(code) {
        padding: 0.2em 0.4em;
        margin: 0;
        font-size: 85%;
        background-color: rgba(27,31,35,0.05);
        border-radius: 3px;
      }

      :deep(pre) {
        padding: 16px;
        overflow: auto;
        font-size: 85%;
        line-height: 1.45;
        background-color: #f6f8fa;
        border-radius: 3px;
      }

      :deep(p:last-child) {
        margin-bottom: 0;
      }
    }
  }
}

// 添加树形选择器样式
:deep(.el-tree-select) {
  width: 100%;
}

/* 评估结果展示样式 */
.evaluation-results {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
  background-color: var(--el-fill-color-lighter);
}

.result-item {
  padding: 12px;
  margin-bottom: 8px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid var(--el-border-color-light);

  &:last-child {
    margin-bottom: 0;
  }
}

.result-title {
  font-weight: 500;
  color: var(--el-color-primary);
  font-size: 14px;
}

.result-text {
  line-height: 1.6;
  margin-top: 4px;
  color: var(--el-text-color-regular);
}

.text-sm {
  font-size: 13px;
}

.mb-1 {
  margin-bottom: 4px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mt-2 {
  margin-top: 8px;
}

.whitespace-pre-line {
  white-space: pre-line;
}

:deep(.el-scrollbar__wrap) {
  padding-right: 12px;
}

.evaluation-text {
  line-height: 1.5;
  padding: 7px 12px;
  background-color: var(--el-fill-color-blank);
  border-radius: 4px;
  border: 1px solid var(--el-border-color-lighter);
  min-height: 32px;
  box-sizing: border-box;
}

.text-gray-400 {
  color: var(--el-text-color-placeholder);
}

/* 评估结果选项样式 */
.evaluation-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.option-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.option-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}
</style>
