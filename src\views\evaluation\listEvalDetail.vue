<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <el-button @click="goBack" type="primary" plain>
              <el-icon class="mr-1"><ArrowLeft /></el-icon>返回
            </el-button>
            <h2 class="ml-4 mb-0">评估清单详情</h2>
          </div>
        </div>
      </template>

      <!-- 基本信息 -->
      <el-descriptions title="基本信息" :column="3" border>
        <el-descriptions-item label="清单名称">{{ listExecution.listName }}</el-descriptions-item>
        <el-descriptions-item label="老人姓名">{{ listExecution.elderName }}</el-descriptions-item>
        <el-descriptions-item label="评估师">{{ listExecution.evaluatorName }}</el-descriptions-item>
        <el-descriptions-item label="评估原因">{{ listExecution.evaluationReason }}</el-descriptions-item>
        <el-descriptions-item label="评估时间">{{ formatDate(listExecution.startTime) }}</el-descriptions-item>
        <el-descriptions-item label="完成时间">{{ formatDate(listExecution.endTime) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="listExecution.status === 1 ? 'success' : 'warning'">
            {{ getDictLabel(DICT_TYPE.EVALUATION_STATUS, listExecution.status) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 评估师分析 -->
      <div class="mt-4">
        <h3>评估师分析</h3>
        <div class="p-4 bg-gray-50 rounded">
          <div v-if="listExecution.evaluatorAnalysis" class="whitespace-pre-wrap">
            {{ listExecution.evaluatorAnalysis }}
          </div>
          <div v-else class="text-gray-400">暂无评估师分析</div>
        </div>
      </div>

      <!-- AI分析 -->
      <div class="mt-4">
        <h3>AI分析</h3>
        <div class="p-4 bg-gray-50 rounded">
          <div v-if="listExecution.aiAnalysis" v-html="formatAiAnalysis(listExecution.aiAnalysis)" class="whitespace-pre-wrap"></div>
          <div v-else class="text-gray-400">暂无AI分析</div>
        </div>
      </div>

      <!-- 模板列表 -->
      <div class="mt-4">
        <h3>评估模板列表</h3>
        <el-table :data="resultList" border style="width: 100%">
          <el-table-column prop="templateName" label="模板名称" min-width="200" />
          <el-table-column prop="evaluationTime" label="评估时间" width="180">
            <template #default="scope">
              {{ formatDate(scope.row.evaluationTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'warning'">
                {{ getDictLabel(DICT_TYPE.EVALUATION_STATUS, scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="scope">
              <el-button link type="primary" @click="handleViewDetail(scope.row)">
                查看详情
              </el-button>
              <el-button link type="primary" @click="handleReEvaluation(scope.row)">
                复评
              </el-button>
              <el-button link type="primary" @click="handleExportSingle(scope.row)">
                导出
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { marked } from 'marked'
import { formatDate } from '@/utils/formatTime'
import { getDictLabel, DICT_TYPE } from '@/utils/dict'
import { useMessage } from '@/hooks/web/useMessage'
import { download } from '@/utils/download'
import { ListExecutionApi } from '@/api/evaluation/listexecution'
import { ResultApi } from '@/api/evaluation/result'
import { useUserStore } from '@/store/modules/user'

const route = useRoute()
const router = useRouter()
const message = useMessage()
const userStore = useUserStore()

// 评估清单执行信息
const listExecution = ref({
  id: null,
  listId: null,
  listName: '',
  elderId: null,
  elderName: '',
  evaluatorId: null,
  evaluatorName: '',
  evaluationReason: '',
  startTime: null,
  endTime: null,
  status: 0,
  evaluatorAnalysis: '',
  aiAnalysis: '',
  requiredTemplateIds: '',
  completedTemplateIds: '',
  resultIds: ''
})

// 评估结果列表
const resultList = ref([])

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 格式化AI分析内容
const formatAiAnalysis = (aiAnalysis) => {
  if (!aiAnalysis) return ''

  // 移除[think]...[/think]标签及其内容
  let formattedAnalysis = aiAnalysis.replace(/\[think\][\s\S]*?\[\/think\]/g, '')

  // 转换Markdown为HTML
  return marked(formattedAnalysis.trim())
}

// 获取评估清单执行信息
const getListExecutionInfo = async () => {
  try {
    const id = route.query.id
    if (!id) {
      ElMessage.error('评估清单执行ID不能为空')
      return
    }

    const data = await ListExecutionApi.getListExecution(Number(id))
    listExecution.value = data

    // 获取关联的评估结果
    await getResultList(Number(id))
  } catch (error) {
    console.error('获取评估清单执行信息失败:', error)
    ElMessage.error('获估清单执行信息失败')
  }
}

// 获取评估结果列表
const getResultList = async (listExecutionId) => {
  try {
    // 使用新的API接口获取评估结果列表
    const results = await ResultApi.getResultsByListExecutionId(Number(listExecutionId))
    resultList.value = results
  } catch (error) {
    console.error('获取评估结果列表失败:', error)
    ElMessage.error('获取评估结果列表失败')
  }
}

// 查看详情
const handleViewDetail = (row) => {
  router.push({
    path: '/evaluation/aiEvalDetail',
    query: {
      id: row.id
    }
  })
}

// 复评
const handleReEvaluation = (row) => {
  router.push({
    path: '/evaluation/aiEvalDetail',
    query: {
      id: row.id,
      isReEvaluation: 'true',
      evaluatorId: userStore.user.id,
      evaluatorName: userStore.user.nickname
    }
  })
}

// 导出单条记录
const handleExportSingle = async (row) => {
  try {
    // 显示加载状态
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在生成评估报告，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      // 导出的二次确认
      await message.exportConfirm()

      // 导出评估结果
      const data = await ResultApi.exportResultWord({ id: row.id })
      const fileName = `评估结果_${row.id}.docx`

      // 使用word下载方法
      download.word(data, fileName)

      // 提示成功
      ElMessage.success('评估报告导出成功')
    } finally {
      // 关闭加载
      loadingInstance.close()
    }
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  }
}

// 页面加载时获取数据
onMounted(() => {
  getListExecutionInfo()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}
</style>
