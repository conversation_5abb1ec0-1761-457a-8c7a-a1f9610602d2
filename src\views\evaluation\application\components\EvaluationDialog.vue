<template>
  <el-dialog
    v-model="dialogVisible"
    :title="evaluationType === 'template' ? '模板评估' : '清单评估'"
    width="90%"
    destroy-on-close
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <div class="evaluation-dialog-content">
      <!-- 主体内容区 -->
      <el-row :gutter="20" class="main-content">
        <!-- 左侧表单区域 -->
        <el-col :xs="24" :sm="24" :md="16" :lg="18" class="form-container">
          <div class="left-column">
            <el-card shadow="hover" v-loading="formLoading" class="form-card">
              <template #header>
                <div class="form-header">
                  <span>{{ evaluationType === 'template' ? '模板评估' : '清单评估' }}</span>
                  <div v-if="evaluationType === 'list' && templateList.length > 0" class="template-tabs">
                    <el-tabs v-model="activeTemplateId" @tab-change="handleTemplateChange">
                      <el-tab-pane
                        v-for="template in templateList"
                        :key="template.id"
                        :label="template.name"
                        :name="template.id.toString()"
                      />
                    </el-tabs>
                  </div>
                </div>
              </template>

              <!-- 评估表单 -->
              <div class="evaluation-form">
                <form-create
                  v-if="!formLoading && templateRule.length > 0"
                  v-model:value="formData"
                  v-model:api="formApi"
                  :rule="templateRule"
                  :option="templateOption"
                  @submit="handleSubmit"
                />
                <div v-else-if="!formLoading" class="no-form">
                  <el-empty description="暂无表单数据" />
                </div>
              </div>
            </el-card>

            <!-- AI分析区域 -->
            <el-card shadow="hover" class="mt-20 analysis-card">
              <template #header>
                <div class="card-header">
                  <span>AI分析</span>
                  <el-button
                    type="primary"
                    link
                    :loading="generatingAI"
                    @click="generateAIAnalysis"
                  >
                    {{ aiAnalysis ? '重新生成' : '生成AI分析' }}
                  </el-button>
                </div>
              </template>
              <div class="ai-analysis" v-loading="generatingAI">
                <div v-if="aiAnalysis" class="ai-content" v-html="formattedAIAnalysis"></div>
                <div v-else class="no-ai">
                  <el-empty description="暂无AI分析，点击生成AI分析按钮生成" />
                </div>
              </div>
            </el-card>
          </div>
        </el-col>

        <!-- 右侧信息区域 -->
        <el-col :xs="24" :sm="24" :md="8" :lg="6" class="info-container">
          <div class="right-column">
            <!-- 老人信息卡片 -->
            <el-card shadow="hover" class="elder-info-card">
              <template #header>
                <div class="card-header">
                  <span>老人信息</span>
                </div>
              </template>
              <div class="elder-info">
                <p><strong>姓名：</strong>{{ elderName }}</p>
                <p><strong>评估原因：</strong>{{ evaluationReason || '无' }}</p>
                <p><strong>评估师：</strong>{{ evaluatorName }}</p>
                <p><strong>评估时间：</strong>{{ formatDate(new Date()) }}</p>
              </div>
            </el-card>

            <!-- 评估信息卡片 -->
            <el-card shadow="hover" class="mt-20 info-card">
              <template #header>
                <div class="card-header">
                  <span>评估信息</span>
                </div>
              </template>
              <div class="evaluation-info">
                <p>
                  <strong>评估类型：</strong>{{ evaluationType === 'template' ? '模板评估' : '清单评估' }}
                </p>
                <p>
                  <strong>{{ evaluationType === 'template' ? '模板名称：' : '清单名称：' }}</strong>
                  {{ evaluationType === 'template' ? templateName : listName }}
                </p>
                <p v-if="evaluationType === 'template'">
                  <strong>模板版本：</strong>{{ templateDetail.version || '无版本号' }}
                </p>
                <p v-if="evaluationType === 'template'">
                  <template
                    v-if="
                      templateDetail.validityStartTimeType === 'fixedDate' &&
                      templateDetail.validityStartTime
                    "
                  >
                    <strong>剩余有效期：</strong>{{ getRemainingValidity() }} 天 (总有效期
                    {{ templateDetail.validityPeriod || 3 }}
                    {{ getValidityUnitText(templateDetail.validityUnit || 'month') }}，从
                    {{ templateDetail.validityStartTime }}
                    起)
                  </template>
                  <template v-else>
                    <strong>有效期：</strong>{{ templateDetail.validityPeriod || 3 }}
                    {{ getValidityUnitText(templateDetail.validityUnit || 'month') }} (从评估时间起)
                  </template>
                </p>
              </div>
            </el-card>

            <!-- 评估师分析 -->
            <el-card shadow="hover" class="mt-20 info-card">
              <template #header>
                <div class="card-header">
                  <span>评估师分析</span>
                </div>
              </template>
              <div class="evaluator-analysis">
                <el-input
                  v-model="evaluatorAnalysis"
                  type="textarea"
                  :rows="6"
                  placeholder="请输入评估师分析..."
                />
              </div>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="success" @click="handleSubmit">提交</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import { formatDate } from '@/utils/formatTime'
import { TemplateApi } from '@/api/evaluation/template'
import { ListApi } from '@/api/evaluation/list'
import { ResultApi } from '@/api/evaluation/result'
import FormCreate from '@form-create/element-ui'
import { marked } from 'marked'

defineOptions({ name: 'EvaluationDialog' })

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  evaluationType: {
    type: String,
    default: 'template'
  },
  templateId: {
    type: [Number, String],
    default: null
  },
  templateName: {
    type: String,
    default: ''
  },
  listId: {
    type: [Number, String],
    default: null
  },
  listName: {
    type: String,
    default: ''
  },
  elderId: {
    type: [Number, String],
    default: null
  },
  elderName: {
    type: String,
    default: ''
  },
  evaluatorId: {
    type: [Number, String],
    default: null
  },
  evaluatorName: {
    type: String,
    default: ''
  },
  evaluationReason: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'save', 'submit', 'close'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 消息提示
const message = useMessage()

// 表单相关
const formLoading = ref(false)
const formData = ref({})
const formApi = ref(null)
const templateRule = ref([])
const templateOption = ref({
  submitBtn: false,
  resetBtn: false,
  form: {
    labelWidth: '120px',
    labelPosition: 'right'
  }
})

// 模板详情
const templateDetail = ref({
  version: '',
  type: 0, // 添加模板类型字段
  validityPeriod: 3,
  validityUnit: 'month',
  validityStartTimeType: 'evaluationTime',
  validityStartTime: ''
})

// 清单相关
const templateList = ref([])
const activeTemplateId = ref('')

// AI分析相关
const aiAnalysis = ref('')
const generatingAI = ref(false)
const formattedAIAnalysis = computed(() => {
  return aiAnalysis.value ? marked(aiAnalysis.value) : ''
})

// 评估师分析
const evaluatorAnalysis = ref('')

// 监听对话框可见性变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initEvaluation()
  }
})

// 初始化评估
const initEvaluation = async () => {
  if (props.evaluationType === 'template') {
    await loadTemplateForm(props.templateId)
  } else {
    await loadListInfo()
  }
}

// 加载模板表单
const loadTemplateForm = async (templateId) => {
  if (!templateId) return

  formLoading.value = true
  try {
    const data = await TemplateApi.getTemplate(Number(templateId))

    // 更新模板详情
    templateDetail.value = {
      version: data.version || 'v1.0',
      type: data.type || 0, // 设置模板类型
      validityPeriod: data.validityPeriod || 3,
      validityUnit: data.validityUnit || 'month',
      validityStartTimeType: data.validityStartTimeType || 'evaluationTime',
      validityStartTime: data.validityStartTime || ''
    }

    if (data.formSchema) {
      try {
        const schema = JSON.parse(data.formSchema)
        templateRule.value = schema.rule || []

        // 设置模板选项
        templateOption.value = {
          ...templateOption.value,
          form: {
            ...templateOption.value.form,
            ...(schema.option?.form || {})
          }
        }

        // 提取有效期设置
        if (schema.option && schema.option.form) {
          templateDetail.value.validityPeriod = schema.option.form.validityPeriod || 3
          templateDetail.value.validityUnit = schema.option.form.validityUnit || 'month'
          templateDetail.value.validityStartTimeType = schema.option.form.validityStartTimeType || 'evaluationTime'
          templateDetail.value.validityStartTime = schema.option.form.validityStartTime || ''
        }
      } catch (error) {
        console.error('解析模板数据失败:', error)
        message.error('模板数据格式错误')
        templateRule.value = []
      }
    }
  } catch (error) {
    console.error('加载模板失败:', error)
    message.error('加载模板失败')
  } finally {
    formLoading.value = false
  }
}

// 加载清单信息
const loadListInfo = async () => {
  if (!props.listId) return

  formLoading.value = true
  try {
    const list = await ListApi.getList(Number(props.listId))
    if (list && list.templateIds) {
      const ids = list.templateIds.split(',')
      if (ids.length > 0) {
        // 获取模板列表
        const templates = await TemplateApi.getSimpleTemplateList()
        templateList.value = templates.filter(t => ids.includes(t.id.toString()))

        if (templateList.value.length > 0) {
          // 默认选中第一个模板
          activeTemplateId.value = templateList.value[0].id.toString()
          await loadTemplateForm(activeTemplateId.value)
        }
      }
    }
  } catch (error) {
    console.error('加载清单信息失败:', error)
    message.error('加载清单信息失败')
  } finally {
    formLoading.value = false
  }
}

// 处理模板切换
const handleTemplateChange = async (templateId) => {
  await loadTemplateForm(templateId)
}

// 获取有效期单位文本
const getValidityUnitText = (unit) => {
  const unitMap = {
    day: '天',
    week: '周',
    month: '月',
    year: '年'
  }
  return unitMap[unit] || '月'
}

// 计算剩余有效期天数
const getRemainingValidity = () => {
  if (!templateDetail.value.validityStartTime) return 0

  const startDate = new Date(templateDetail.value.validityStartTime)
  const today = new Date()

  // 计算有效期结束日期
  let endDate = new Date(startDate)
  const period = templateDetail.value.validityPeriod || 3
  const unit = templateDetail.value.validityUnit || 'month'

  switch (unit) {
    case 'day':
      endDate.setDate(endDate.getDate() + period)
      break
    case 'week':
      endDate.setDate(endDate.getDate() + period * 7)
      break
    case 'month':
      endDate.setMonth(endDate.getMonth() + period)
      break
    case 'year':
      endDate.setFullYear(endDate.getFullYear() + period)
      break
  }

  // 计算剩余天数
  const diffTime = endDate.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  return diffDays > 0 ? diffDays : 0
}

// 计算评分
const calculateScore = (formValues) => {
  // 如果没有表单数据，返回0
  if (!formValues || Object.keys(formValues).length === 0) {
    return 0
  }

  // 计算所有数值字段的总和
  let totalScore = 0
  let count = 0

  Object.values(formValues).forEach(value => {
    // 如果值是数字，加入总分
    if (typeof value === 'number') {
      totalScore += value
      count++
    } else if (typeof value === 'string' && !isNaN(Number(value))) {
      // 如果值是数字字符串，转换后加入总分
      totalScore += Number(value)
      count++
    }
  })

  // 计算平均分，保留2位小数
  return count > 0 ? Math.round((totalScore / count) * 100) / 100 : 0
}

// 生成AI分析
const generateAIAnalysis = async () => {
  if (generatingAI.value) return

  generatingAI.value = true

  try {
    // 模拟AI分析生成
    setTimeout(() => {
      aiAnalysis.value = `# 评估分析报告\n\n## 基本情况\n老人 **${props.elderName}** 进行了${props.evaluationType === 'template' ? props.templateName : props.listName}评估。\n\n## 评估结果\n根据评估数据分析，老人的主要情况如下：\n\n1. 生活自理能力良好\n2. 认知功能正常\n3. 情绪状态稳定\n\n## 建议\n建议针对老人的情况，提供以下服务：\n\n- 定期健康检查\n- 社交活动参与\n- 适当的体育锻炼`
      generatingAI.value = false
    }, 2000)
  } catch (error) {
    console.error('生成AI分析失败:', error)
    message.error('生成AI分析失败')
    generatingAI.value = false
  }
}

// 保存评估数据
const handleSave = async () => {
  try {
    // 获取表单数据
    let formValues = formApi.value ? formApi.value.formData() : {}

    // 确保formValues是一个有效的对象
    if (!formValues || typeof formValues !== 'object') {
      formValues = {}
    }

    // 获取模板选项
    const options = {
      form: {
        templateType: templateDetail.value.type || 0,
        templateRule: JSON.stringify(templateRule.value),
        validityPeriod: templateDetail.value.validityPeriod || 3,
        validityUnit: templateDetail.value.validityUnit || 'month',
        validityStartTimeType: templateDetail.value.validityStartTimeType || 'evaluationTime',
        validityStartTime: templateDetail.value.validityStartTime || ''
      }
    }

    // 计算评分
    const score = calculateScore(formValues)

    // 构建评估结果JSON
    const resultJson = {
      options: options,
      formData: formValues,
      score: score,
      version: templateDetail.value.version || 'v1.0'
    }

    // 构建保存数据
    const saveData = {
      elderId: Number(props.elderId),
      elderName: props.elderName,
      evaluatorId: Number(props.evaluatorId),
      evaluatorName: props.evaluatorName,
      evaluationReason: props.evaluationReason,
      type: 2, // 暂存状态
      evaluationTime: formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss'), // 添加评估时间
      result: JSON.stringify(resultJson), // 使用JSON格式的评估结果
      aiAnalysis: aiAnalysis.value || '',
      evaluatorAnalysis: evaluatorAnalysis.value || '',
      aiInputs: '', // 暂时为空
      templateId: props.evaluationType === 'template' ? Number(props.templateId) : null,
      templateName: props.evaluationType === 'template' ? props.templateName : ''
    }

    // 如果是清单评估，还需要设置当前选中的模板ID
    if (props.evaluationType === 'list' && activeTemplateId.value) {
      saveData.templateId = Number(activeTemplateId.value)
      const template = templateList.value.find(t => t.id.toString() === activeTemplateId.value)
      saveData.templateName = template ? template.name : ''
    }

    // 打印保存数据，用于调试
    console.log('保存数据:', saveData)
    console.log('result类型:', typeof saveData.result)
    console.log('result内容:', saveData.result)

    // 调用保存API
    await ResultApi.createResult(saveData)
    message.success('保存成功')
    emit('save', saveData)
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败: ' + (error.response?.data?.msg || error.message))
  }
}

// 提交评估数据
const handleSubmit = async () => {
  try {
    // 获取表单数据
    let formValues = formApi.value ? formApi.value.formData() : {}

    // 确保formValues是一个有效的对象
    if (!formValues || typeof formValues !== 'object') {
      formValues = {}
    }

    // 获取模板选项
    const options = {
      form: {
        templateType: templateDetail.value.type || 0,
        templateRule: JSON.stringify(templateRule.value),
        validityPeriod: templateDetail.value.validityPeriod || 3,
        validityUnit: templateDetail.value.validityUnit || 'month',
        validityStartTimeType: templateDetail.value.validityStartTimeType || 'evaluationTime',
        validityStartTime: templateDetail.value.validityStartTime || ''
      }
    }

    // 计算评分
    const score = calculateScore(formValues)

    // 构建评估结果JSON
    const resultJson = {
      options: options,
      formData: formValues,
      score: score,
      version: templateDetail.value.version || 'v1.0'
    }

    // 构建提交数据
    const submitData = {
      elderId: Number(props.elderId),
      elderName: props.elderName,
      evaluatorId: Number(props.evaluatorId),
      evaluatorName: props.evaluatorName,
      evaluationReason: props.evaluationReason,
      type: 0, // 正式评估类型
      evaluationTime: formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss'), // 添加评估时间
      result: JSON.stringify(resultJson), // 使用JSON格式的评估结果
      aiAnalysis: aiAnalysis.value || '',
      evaluatorAnalysis: evaluatorAnalysis.value || '',
      aiInputs: '', // 暂时为空
      templateId: props.evaluationType === 'template' ? Number(props.templateId) : null,
      templateName: props.evaluationType === 'template' ? props.templateName : ''
    }

    // 如果是清单评估，还需要设置当前选中的模板ID
    if (props.evaluationType === 'list' && activeTemplateId.value) {
      submitData.templateId = Number(activeTemplateId.value)
      const template = templateList.value.find(t => t.id.toString() === activeTemplateId.value)
      submitData.templateName = template ? template.name : ''
    }

    // 打印提交数据，用于调试
    console.log('提交数据:', submitData)
    console.log('result类型:', typeof submitData.result)
    console.log('result内容:', submitData.result)

    // 调用提交API
    await ResultApi.createResult(submitData)
    message.success('提交成功')
    emit('submit', submitData)
    dialogVisible.value = false
  } catch (error) {
    console.error('提交失败:', error)
    message.error('提交失败: ' + (error.response?.data?.msg || error.message))
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  emit('close')
}

// 初始化
onMounted(() => {
  if (dialogVisible.value) {
    initEvaluation()
  }
})
</script>

<style lang="scss" scoped>
.evaluation-dialog-content {
  max-height: 70vh;
  overflow-y: auto;

  .main-content {
    display: flex;
    flex-wrap: wrap;
    height: 65vh;
  }

  .form-container {
    height: 100%;
    max-width: 75%; /* 限制左侧容器最大宽度 */

    .left-column {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .form-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      max-width: 100%; /* 确保卡片不超出容器 */

      :deep(.el-card__body) {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        max-width: 100%; /* 确保卡片内容不超出 */
      }
    }

    .form-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;

      .template-tabs {
        margin-top: 10px;
        width: 100%;
      }
    }

    .evaluation-form {
      flex: 1;
      overflow-y: auto;
      min-height: 300px;
      max-width: 100%;
      word-wrap: break-word;
      word-break: break-all;

      /* 确保表单内容不会超出容器宽度 */
      :deep(.el-form) {
        max-width: 100%;
      }

      :deep(.el-form-item) {
        max-width: 100%;

        .el-form-item__label {
          word-wrap: break-word;
          word-break: break-all;
          white-space: normal;
          line-height: 1.4;
        }

        .el-form-item__content {
          max-width: 100%;

          .el-input,
          .el-textarea,
          .el-select,
          .el-radio-group,
          .el-checkbox-group {
            max-width: 100%;
          }

          .el-input__inner,
          .el-textarea__inner {
            word-wrap: break-word;
            word-break: break-all;
          }
        }
      }
    }

    .no-form {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 300px;
    }

    .analysis-card {
      max-height: 250px;
      display: flex;
      flex-direction: column;

      :deep(.el-card__body) {
        flex: 1;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
      }
    }
  }

  .info-container {
    height: 100%;
    min-width: 25%; /* 确保右侧容器最小宽度 */
    max-width: 25%; /* 限制右侧容器最大宽度 */

    .right-column {
      height: 100%;
      display: flex;
      flex-direction: column;
      min-width: 100%;
    }

    .elder-info-card {
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-bottom: 20px;

      :deep(.el-card__body) {
        flex: 1;
        overflow-y: visible;
      }
    }

    .info-card {
      max-height: 250px;
      display: flex;
      flex-direction: column;

      :deep(.el-card__body) {
        flex: 1;
        overflow-y: visible;
      }
    }

    .card-header {
      font-weight: bold;
    }

    .elder-info,
    .evaluation-info,
    .evaluator-analysis {
      p {
        margin: 8px 0;
      }
    }
  }

  .ai-analysis {
    flex: 1;
    overflow-y: auto;
    min-height: 200px;

    /* 隐藏滚动条但保留滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }

    .ai-content {
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 4px;
      max-height: 180px;
      overflow-y: auto;

      /* 隐藏滚动条但保留滚动功能 */
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }

      :deep(h1) {
        font-size: 1.5rem;
        margin-top: 0;
      }

      :deep(h2) {
        font-size: 1.3rem;
      }

      :deep(ul) {
        padding-left: 20px;
      }
    }

    .no-ai {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
  }

  .mt-20 {
    margin-top: 20px;
  }
}
</style>
