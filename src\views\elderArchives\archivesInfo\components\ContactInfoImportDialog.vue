  <template>
  <Dialog :title="`导入联系人信息 - ${elderName}`" v-model="dialogVisible" width="1200px">
    <div class="contact-import-container">
      <!-- 信息提供者部分 -->
      <el-card class="mb-4">
        <template #header>
          <div class="flex items-center justify-between">
            <span class="font-medium">信息提供者</span>
            <el-button type="primary" size="small" @click="addInfoProvider">
              <Icon icon="ep:plus" class="mr-1" />
              添加信息提供者
            </el-button>
          </div>
        </template>
        <div v-if="infoProviders.length === 0" class="text-center text-gray-500 py-4">
          暂无信息提供者，点击上方按钮添加
        </div>
        <div v-else>
          <el-table :data="infoProviders" border size="small">
            <el-table-column label="状态" width="80">
              <template #default="{ row }">
                <el-tag v-if="row.id" type="success" size="small">已存在</el-tag>
                <el-tag v-else type="warning" size="small">新增</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="姓名" width="120">
              <template #default="{ row }">
                <el-input v-model="row.name" placeholder="请输入姓名" size="small" :disabled="!!row.id" />
              </template>
            </el-table-column>
            <el-table-column prop="gender" label="性别" width="100">
              <template #default="{ row }">
                <el-select v-model="row.gender" placeholder="选择性别" size="small" :disabled="!!row.id">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="relationship" label="关系" width="120">
              <template #default="{ row }">
                <el-select v-model="row.relationship" placeholder="选择关系" size="small" :disabled="!!row.id">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_CONTACT_RELATIONSHIP)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="isEscort" label="是否带领入住" width="120">
              <template #default="{ row }">
                <el-select v-model="row.isEscort" placeholder="选择" size="small" :disabled="!!row.id">
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="手机号" width="140">
              <template #default="{ row }">
                <el-input v-model="row.phone" placeholder="请输入手机号" size="small" :disabled="!!row.id" />
              </template>
            </el-table-column>
            <el-table-column prop="idCard" label="身份证号" width="160">
              <template #default="{ row }">
                <el-input v-model="row.idCard" placeholder="请输入身份证号" size="small" :disabled="!!row.id" />
              </template>
            </el-table-column>
            <el-table-column prop="address" label="联系地址" width="180">
              <template #default="{ row }">
                <el-input v-model="row.address" placeholder="请输入联系地址" size="small" :disabled="!!row.id" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ row, $index }">
                <el-button
                  type="danger"
                  size="small"
                  link
                  @click="removeInfoProvider(row, $index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <!-- 紧急联系人部分 -->
      <el-card>
        <template #header>
          <div class="flex items-center justify-between">
            <span class="font-medium">紧急联系人</span>
            <el-button type="primary" size="small" @click="addEmergencyContact">
              <Icon icon="ep:plus" class="mr-1" />
              添加紧急联系人
            </el-button>
          </div>
        </template>
        <div v-if="emergencyContacts.length === 0" class="text-center text-gray-500 py-4">
          暂无紧急联系人，点击上方按钮添加
        </div>
        <div v-else>
          <el-table :data="emergencyContacts" border size="small">
            <el-table-column label="状态" width="80">
              <template #default="{ row }">
                <el-tag v-if="row.id" type="success" size="small">已存在</el-tag>
                <el-tag v-else type="warning" size="small">新增</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="姓名" width="120">
              <template #default="{ row }">
                <el-input v-model="row.name" placeholder="请输入姓名" size="small" :disabled="!!row.id" />
              </template>
            </el-table-column>
            <el-table-column prop="gender" label="性别" width="100">
              <template #default="{ row }">
                <el-select v-model="row.gender" placeholder="选择性别" size="small" :disabled="!!row.id">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="relationship" label="关系" width="120">
              <template #default="{ row }">
                <el-select v-model="row.relationship" placeholder="选择关系" size="small" :disabled="!!row.id">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_CONTACT_RELATIONSHIP)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="手机号" width="140">
              <template #default="{ row }">
                <el-input v-model="row.phone" placeholder="请输入手机号" size="small" :disabled="!!row.id" />
              </template>
            </el-table-column>
            <el-table-column prop="idCard" label="身份证号" width="160">
              <template #default="{ row }">
                <el-input v-model="row.idCard" placeholder="请输入身份证号" size="small" :disabled="!!row.id" />
              </template>
            </el-table-column>
            <el-table-column prop="address" label="联系地址" width="180">
              <template #default="{ row }">
                <el-input v-model="row.address" placeholder="请输入联系地址" size="small" :disabled="!!row.id" />
              </template>
            </el-table-column>
            <el-table-column prop="emergencyPriority" label="优先级" width="100">
              <template #default="{ row }">
                <el-select v-model="row.emergencyPriority" placeholder="选择优先级" size="small" :disabled="!!row.id">
                  <el-option
                    v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_CONTACT_EMERGENCY_PRIORITY)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ row, $index }">
                <el-button
                  type="danger"
                  size="small"
                  link
                  @click="removeEmergencyContact(row, $index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitForm" :loading="formLoading">确 定</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { ContactInfoApi, ContactInfoVO } from '@/api/elderArchives/contactinfo'

/** 联系人信息导入对话框 */
defineOptions({ name: 'ContactInfoImportDialog' })

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中
const elderId = ref<number>() // 老人ID
const elderName = ref<string>('') // 老人姓名

// 信息提供者列表
const infoProviders = ref<ContactInfoVO[]>([])
// 紧急联系人列表
const emergencyContacts = ref<ContactInfoVO[]>([])

/** 打开弹窗 */
const open = async (id: number, name: string) => {
  dialogVisible.value = true
  elderId.value = id
  elderName.value = name
  resetForm()
  // 加载现有联系人信息
  await loadExistingContacts()
}

/** 重置表单 */
const resetForm = () => {
  infoProviders.value = []
  emergencyContacts.value = []
  formLoading.value = false
}

/** 加载现有联系人信息 */
const loadExistingContacts = async () => {
  if (!elderId.value) return

  try {
    const data = await ContactInfoApi.getContactInfoPage({
      elderId: elderId.value,
      pageNo: 1,
      pageSize: 100 // 获取所有联系人
    })

    if (data && data.list) {
      // 筛选信息提供者（所有非紧急联系人都作为信息提供者显示）
      infoProviders.value = data.list.filter(contact => !contact.isEmergency)

      // 筛选紧急联系人
      emergencyContacts.value = data.list.filter(contact => contact.isEmergency)
        .sort((a, b) => (a.emergencyPriority || 999) - (b.emergencyPriority || 999)) // 按优先级排序
    }
  } catch (error: any) {
    console.error('获取联系人信息失败:', error)
    message.error('获取联系人信息失败')
  }
}

/** 添加信息提供者 */
const addInfoProvider = () => {
  infoProviders.value.push({
    id: undefined,
    elderId: elderId.value,
    name: '',
    gender: undefined,
    relationship: undefined,
    isEscort: true, // 信息提供者默认为带领入住
    phone: '',
    idCard: '',
    address: '',
    isEmergency: false,
    emergencyPriority: undefined,
    remark: ''
  })
}

/** 删除信息提供者 */
const removeInfoProvider = async (row: ContactInfoVO, index: number) => {
  // 如果是现有数据，需要调用API删除
  if (row.id) {
    try {
      await message.delConfirm('确定要删除这条联系人信息吗？')
      await ContactInfoApi.deleteContactInfo(row.id)
      message.success('删除成功')
    } catch (error) {
      console.error('删除联系人信息失败:', error)
      if (error !== 'cancel') {
        message.error('删除失败，请重试')
      }
      return
    }
  }

  // 从列表中移除
  infoProviders.value.splice(index, 1)
}

/** 添加紧急联系人 */
const addEmergencyContact = () => {
  emergencyContacts.value.push({
    id: undefined,
    elderId: elderId.value,
    name: '',
    gender: undefined,
    relationship: undefined,
    isEscort: false,
    phone: '',
    idCard: '',
    address: '',
    isEmergency: true,
    emergencyPriority: 1,
    remark: ''
  })
}

/** 删除紧急联系人 */
const removeEmergencyContact = async (row: ContactInfoVO, index: number) => {
  // 如果是现有数据，需要调用API删除
  if (row.id) {
    try {
      await message.delConfirm('确定要删除这条联系人信息吗？')
      await ContactInfoApi.deleteContactInfo(row.id)
      message.success('删除成功')
    } catch (error) {
      console.error('删除联系人信息失败:', error)
      if (error !== 'cancel') {
        message.error('删除失败，请重试')
      }
      return
    }
  }

  // 从列表中移除
  emergencyContacts.value.splice(index, 1)
}

/** 验证手机号格式 */
const validatePhone = (phone: string) => {
  if (!phone) return false
  // 手机号必须是11位数字，以1开头，第二位是3-9
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/** 验证身份证号格式 */
const validateIdCard = (idCard: string) => {
  if (!idCard) return true // 身份证号不是必填，为空时通过验证
  // 身份证号必须是18位，前17位是数字，最后一位是数字或大写X
  const idCardRegex = /^[\d]{17}[\dX]$/
  if (!idCardRegex.test(idCard)) return false

  // 检查是否包含小写x
  if (idCard.includes('x')) return false

  // 检查X是否只在最后一位
  if (idCard.indexOf('X') !== -1 && idCard.indexOf('X') !== 17) return false

  return true
}

/** 验证表单数据 */
const validateForm = () => {
  // 只验证新增的信息提供者
  const newInfoProviders = infoProviders.value.filter(provider => !provider.id)
  for (let i = 0; i < newInfoProviders.length; i++) {
    const provider = newInfoProviders[i]
    if (!provider.name) {
      message.error(`新增信息提供者第${i + 1}行：姓名不能为空`)
      return false
    }
    if (!provider.gender) {
      message.error(`新增信息提供者第${i + 1}行：性别不能为空`)
      return false
    }
    if (!provider.relationship) {
      message.error(`新增信息提供者第${i + 1}行：关系不能为空`)
      return false
    }
    if (!provider.phone) {
      message.error(`新增信息提供者第${i + 1}行：手机号不能为空`)
      return false
    }
    // 验证手机号格式
    if (!validatePhone(provider.phone)) {
      message.error(`新增信息提供者第${i + 1}行：手机号格式不正确，请输入11位有效手机号`)
      return false
    }
    // 验证身份证号格式
    if (!validateIdCard(provider.idCard)) {
      message.error(`新增信息提供者第${i + 1}行：身份证号格式不正确，请输入18位有效身份证号`)
      return false
    }
    if (provider.isEscort === undefined || provider.isEscort === null) {
      message.error(`新增信息提供者第${i + 1}行：是否带领入住不能为空`)
      return false
    }
  }

  // 只验证新增的紧急联系人
  const newEmergencyContacts = emergencyContacts.value.filter(contact => !contact.id)
  for (let i = 0; i < newEmergencyContacts.length; i++) {
    const contact = newEmergencyContacts[i]
    if (!contact.name) {
      message.error(`新增紧急联系人第${i + 1}行：姓名不能为空`)
      return false
    }
    if (!contact.gender) {
      message.error(`新增紧急联系人第${i + 1}行：性别不能为空`)
      return false
    }
    if (!contact.relationship) {
      message.error(`新增紧急联系人第${i + 1}行：关系不能为空`)
      return false
    }
    if (!contact.phone) {
      message.error(`新增紧急联系人第${i + 1}行：手机号不能为空`)
      return false
    }
    // 验证手机号格式
    if (!validatePhone(contact.phone)) {
      message.error(`新增紧急联系人第${i + 1}行：手机号格式不正确，请输入11位有效手机号`)
      return false
    }
    // 验证身份证号格式
    if (!validateIdCard(contact.idCard)) {
      message.error(`新增紧急联系人第${i + 1}行：身份证号格式不正确，请输入18位有效身份证号`)
      return false
    }
    if (!contact.emergencyPriority) {
      message.error(`新增紧急联系人第${i + 1}行：优先级不能为空`)
      return false
    }
  }

  return true
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 获取新增的联系人数据
  const newInfoProviders = infoProviders.value.filter(provider => !provider.id)
  const newEmergencyContacts = emergencyContacts.value.filter(contact => !contact.id)

  // 检查是否有新增数据
  if (newInfoProviders.length === 0 && newEmergencyContacts.length === 0) {
    message.success('没有新增数据需要保存')
    dialogVisible.value = false
    emit('success')
    return
  }

  // 验证表单
  if (!validateForm()) {
    return
  }

  formLoading.value = true
  try {
    // 合并新增的联系人数据
    const newContacts = [...newInfoProviders, ...newEmergencyContacts]

    // 批量创建联系人信息，记录成功和失败的数量
    let successCount = 0
    let failedCount = 0
    const failedContacts: string[] = []

    for (let i = 0; i < newContacts.length; i++) {
      const contact = newContacts[i]
      try {
        await ContactInfoApi.createContactInfo(contact)
        successCount++
      } catch (error) {
        failedCount++
        const contactType = contact.isEmergency ? '紧急联系人' : '信息提供者'
        failedContacts.push(`${contactType}：${contact.name || '未填写姓名'}`)
        console.error(`创建联系人信息失败:`, contact, error)
      }
    }

    // 根据结果显示不同的提示信息
    if (failedCount === 0) {
      message.success(`成功新增 ${successCount} 条联系人信息`)
      dialogVisible.value = false
      emit('success')
    } else if (successCount === 0) {
      message.error(`所有联系人信息创建失败，请检查数据后重试`)
    } else {
      message.warning(`成功新增 ${successCount} 条，失败 ${failedCount} 条联系人信息。失败的联系人：${failedContacts.join('、')}`)
      // 部分成功时也关闭对话框并刷新，让用户看到已成功创建的数据
      dialogVisible.value = false
      emit('success')
    }
  } catch (error) {
    console.error('导入联系人信息过程中发生未知错误:', error)
    message.error('导入联系人信息失败，请重试')
  } finally {
    formLoading.value = false
  }
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped>
.contact-import-container {
  max-height: 600px;
  overflow-y: auto;
}

.mb-4 {
  margin-bottom: 1rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.font-medium {
  font-weight: 500;
}

.text-center {
  text-align: center;
}

.text-gray-500 {
  color: #6b7280;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.mr-1 {
  margin-right: 0.25rem;
}
</style>
