<script lang="ts">
// 默认导出组件
export default {
  name: 'SummaryAnalysis'
}
</script>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { marked } from 'marked'
import { ArrowDown, InfoFilled } from '@element-plus/icons-vue'
import { ListExecutionApi } from '@/api/evaluation/listexecution'
import { ResultApi } from '@/api/evaluation/result'
import { TemplateApi } from '@/api/evaluation/template'
import { AiEvaluationApi } from '@/api/ai/evaluation'
import { ModelApi } from '@/api/ai/model/model'

// 获取路由实例
const router = useRouter()

// 定义props接收组件传值
const props = defineProps({
  listExecutionInfo: {
    type: Object,
    required: true
  },
  // 是否只读模式
  readonly: {
    type: Boolean,
    default: false
  },
  directQuery: {
    type: String,
    default: ''
  }
})

// 定义事件
const emit = defineEmits(['update:listExecutionInfo', 'saved', 'close', 'evaluationComplete'])

// 响应式状态
const loading = ref(false)
const saving = ref(false)
const generatingAI = ref(false)

// 评估师分析和AI分析
const evaluatorAnalysis = ref<string>('')
const aiAnalysis = ref<string>('')
const aiInputs = ref<string>('')
const summaryResult = ref<string>('')

// 编辑状态
const isEditing = ref<boolean>(false)

// AI分析对话框相关
const dialogVisible = ref(false)
const aiReasoning = ref('')
const tempReasoning = ref('')
const tempAnalysis = ref('')
const isAnalysisFinish = ref(false)
const isReasoningCollapsed = ref(['1']) // 默认折叠分析过程
let abortController: AbortController | null = null
const taskId = ref('')

// AI模型相关变量
const aiModelList = ref([])
const selectedModelId = ref<number | null>(null)

// 监听props中的listExecutionInfo变化
watch(
  () => props.listExecutionInfo,
  (newInfo) => {
    if (newInfo) {
      // 先清空所有AI分析相关变量
      evaluatorAnalysis.value = ''
      aiAnalysis.value = ''
      aiInputs.value = ''
      aiReasoning.value = ''
      tempReasoning.value = ''
      tempAnalysis.value = ''
      isAnalysisFinish.value = false
      summaryResult.value = ''

      // 然后从newInfo中加载数据
      evaluatorAnalysis.value = newInfo.evaluatorAnalysis || ''
      aiInputs.value = newInfo.aiInputs || ''
      summaryResult.value = newInfo.summaryResult || ''

      // 只有当newInfo中有aiAnalysis时才加载
      if (newInfo.aiAnalysis) {
        // 如果AI分析中包含思考过程，分离出来
        if (newInfo.aiAnalysis.includes('[think]')) {
          const parts = newInfo.aiAnalysis.split('[/think]')
          if (parts.length === 2) {
            aiReasoning.value = parts[0].replace('[think]', '').trim()
            aiAnalysis.value = parts[1].trim()
          } else {
            aiAnalysis.value = newInfo.aiAnalysis
          }
        } else {
          aiAnalysis.value = newInfo.aiAnalysis
        }
      }
    }
  },
  { immediate: true, deep: true }
)

// 加载AI模型列表
const loadAiModels = async () => {
  try {
    const models = await ModelApi.getModelSimpleList(1) // 1表示聊天模型类型
    // 过滤掉包含"dify"的模型（不区分大小写）
    aiModelList.value = (models || []).filter(model => {
      const modelName = (model.name || '').toLowerCase()
      const modelPlatform = (model.platform || '').toLowerCase()
      const modelModel = (model.model || '').toLowerCase()
      return !modelName.includes('dify') &&
             !modelPlatform.includes('dify') &&
             !modelModel.includes('dify')
    })

    // 优先选择DeepSeek-R1相关模型
    if (aiModelList.value.length > 0) {
      // 查找DeepSeek-R1相关模型
      const deepSeekR1Model = aiModelList.value.find(model =>
        model.name && (
          model.name.toLowerCase().includes('deepseek-r1') ||
          model.name.toLowerCase().includes('deepseek r1') ||
          model.model && model.model.toLowerCase().includes('deepseek-r1')
        )
      )

      if (deepSeekR1Model) {
        selectedModelId.value = deepSeekR1Model.id
      } else {
        // 如果没有找到DeepSeek-R1，则选择第一个
        selectedModelId.value = aiModelList.value[0].id
      }
    }
  } catch (error) {
    console.error('加载AI模型列表失败:', error)
    ElMessage.warning('加载AI模型列表失败')
  }
}

// 组件挂载时加载AI模型
onMounted(() => {
  loadAiModels()
})

// 切换编辑状态
const toggleEdit = () => {
  isEditing.value = !isEditing.value
}

// 保存评估师分析
const saveEvaluation = async () => {
  // 检查listExecutionInfo是否存在且id是否有效
  if (!props.listExecutionInfo || !props.listExecutionInfo.id || isNaN(Number(props.listExecutionInfo.id))) {
    ElMessage.error('评估清单执行ID不能为空或无效')
    return
  }

  try {
    saving.value = true

    // 获取当前登录用户ID作为评估师ID
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    const evaluatorId = userInfo.id || props.listExecutionInfo.evaluatorId || props.listExecutionInfo.createBy

    if (!evaluatorId) {
      console.warn('未找到评估师ID，将使用默认值')
    }

    // 获取评估师姓名
    const evaluatorName = userInfo.nickname || props.listExecutionInfo.evaluatorName || '未知评估师'

    // 准备更新数据（排除listRule避免重复计算分数）
    const updateData = {
      id: Number(props.listExecutionInfo.id),
      listId: props.listExecutionInfo.listId,
      listName: props.listExecutionInfo.listName,
      elderId: props.listExecutionInfo.elderId,
      elderName: props.listExecutionInfo.elderName,
      evaluatorId: evaluatorId || 1,
      evaluatorName: evaluatorName,
      status: props.listExecutionInfo.status,
      startTime: props.listExecutionInfo.startTime,
      requiredTemplateIds: props.listExecutionInfo.requiredTemplateIds,
      completedTemplateIds: props.listExecutionInfo.completedTemplateIds,
      draftTemplateIds: props.listExecutionInfo.draftTemplateIds,
      resultIds: props.listExecutionInfo.resultIds,
      evaluatorAnalysis: evaluatorAnalysis.value,
      aiInputs: props.listExecutionInfo.aiInputs || '',
      aiAnalysis: props.listExecutionInfo.aiAnalysis || '',
      evaluationReason: props.listExecutionInfo.evaluationReason || '',
      apiKey: props.listExecutionInfo.apiKey || ''
      // 注意：不传递listRule，避免重复计算分数
    }

    // 调用API更新评估师分析
    await ListExecutionApi.updateListExecution(updateData)

    // 更新本地数据
    const updatedInfo = {
      ...props.listExecutionInfo,
      evaluatorAnalysis: evaluatorAnalysis.value,
      evaluatorId: evaluatorId || 1,
      evaluatorName: evaluatorName
    }
    emit('update:listExecutionInfo', updatedInfo)
    emit('saved')

    ElMessage.success('评估师综合分析保存成功')
    isEditing.value = false // 退出编辑模式
  } catch (error) {
    console.error('保存评估师分析失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 直接转换表单数据为可读文本
const convertFormDataToReadable = async (formDataStr: string) => {
  try {
    // 解析查询参数
    const lines = formDataStr.split('\n')
    let currentTemplate = ''
    let formData = {}
    let templateFormData = {}

    // 遍历每一行，提取模板名称和表单数据
    for (const line of lines) {
      if (line.startsWith('模板名称：')) {
        // 如果遇到新的模板，保存之前的数据
        if (currentTemplate && Object.keys(formData).length > 0) {
          templateFormData[currentTemplate] = formData
          formData = {}
        }
        currentTemplate = line.replace('模板名称：', '').trim()
      } else if (line.trim().startsWith('F') && line.includes(':')) {
        // 提取表单字段和值
        const parts = line.trim().split(':')
        if (parts.length >= 2) {
          const field = parts[0].trim()
          const value = parts[1].trim()
          formData[field] = value
        }
      }
    }

    // 保存最后一个模板的数据
    if (currentTemplate && Object.keys(formData).length > 0) {
      templateFormData[currentTemplate] = formData
    }

    console.log('提取的模板表单数据:', templateFormData)

    // 获取所有模板ID
    const templateNames = Object.keys(templateFormData)
    if (templateNames.length === 0) {
      return formDataStr
    }

    // 获取所有模板的规则和结果数据
    const templateRules = {}
    const templateResults = {}

    // 首先尝试获取评估结果数据
    try {
      if (props.listExecutionInfo && props.listExecutionInfo.id) {
        const results = await ResultApi.getResultsByListExecutionId(props.listExecutionInfo.id)
        if (results && results.length > 0) {
          for (const result of results) {
            if (result.templateName && result.result) {
              try {
                const parsedResult = JSON.parse(result.result)
                if (parsedResult.hierarchicalResults || parsedResult.fullResult?.hierarchicalResults) {
                  templateResults[result.templateName] = {
                    hierarchicalResults: parsedResult.hierarchicalResults || parsedResult.fullResult?.hierarchicalResults,
                    aiInput: parsedResult.fullResult?.aiInput
                  }
                  console.log(`获取到模板${result.templateName}的层次化数据`)
                }
              } catch (error) {
                console.error(`解析模板${result.templateName}结果数据失败:`, error)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('获取评估结果数据失败:', error)
    }

    // 获取所有模板的规则
    for (const templateName of templateNames) {
      try {
        // 查询模板ID
        const templatesResponse = await TemplateApi.getTemplatePage({
          name: templateName,
          pageNo: 1,
          pageSize: 10
        })

        if (templatesResponse && templatesResponse.list && templatesResponse.list.length > 0) {
          const templateId = templatesResponse.list[0].id

          // 获取模板详情
          const templateData = await TemplateApi.getTemplate(templateId)
          if (templateData && templateData.formSchema) {
            // 解析表单规则
            const schema = typeof templateData.formSchema === 'string'
              ? JSON.parse(templateData.formSchema)
              : templateData.formSchema

            if (schema && schema.rule) {
              templateRules[templateName] = schema.rule
              console.log(`获取到模板${templateName}的规则:`, schema.rule)
            }
          }
        }
      } catch (error) {
        console.error(`获取模板${templateName}规则失败:`, error)
      }
    }

    // 转换每个模板的表单数据
    let convertedText = ''
    for (const templateName of templateNames) {
      convertedText += `\n模板名称：${templateName}\n`

      // 首先尝试使用层次化结果数据
      if (templateResults[templateName] && templateResults[templateName].hierarchicalResults) {
        const hierarchicalResults = templateResults[templateName].hierarchicalResults

        // 使用层次化结果构建可读文本
        convertedText += `表单数据：\n`

        // 遍历每个顶级分类
        for (const category in hierarchicalResults) {
          const categoryData = hierarchicalResults[category]
          convertedText += `${category}：\n`

          // 遍历该分类下的所有字段
          if (categoryData.children && categoryData.children.length > 0) {
            categoryData.children.forEach(field => {
              if (field.title && field.displayValue !== undefined) {
                convertedText += `  ${field.title}：${field.displayValue}\n`
              }
            })
          }
        }
      }
      // 如果没有层次化结果，尝试使用aiInput字段
      else if (templateResults[templateName] && templateResults[templateName].aiInput) {
        convertedText += templateResults[templateName].aiInput
      }
      // 如果没有上述数据，尝试使用常规方法转换
      else {
        const formData = templateFormData[templateName]
        const formRule = templateRules[templateName]

        if (formRule) {
          try {
            // 调用后端API转换表单数据
            const convertResponse = await ResultApi.convertFormData({
              formData: JSON.stringify(formData),
              formRule: JSON.stringify(formRule)
            })

            if (convertResponse && convertResponse.length > 0) {
              convertedText += `${convertResponse}\n`
            } else {
              // 如果转换失败，使用原始表单数据
              convertedText += `表单数据：\n`
              for (const key in formData) {
                const value = formData[key]
                convertedText += `  ${key}: ${value}\n`
              }
            }
          } catch (error) {
            console.error(`转换模板${templateName}表单数据失败:`, error)
            // 如果转换失败，使用原始表单数据
            convertedText += `表单数据：\n`
            for (const key in formData) {
              const value = formData[key]
              convertedText += `  ${key}: ${value}\n`
            }
          }
        } else {
          // 如果没有规则，直接使用原始表单数据
          convertedText += `表单数据：\n`
          for (const key in formData) {
            const value = formData[key]
            convertedText += `  ${key}: ${value}\n`
          }
        }
      }
    }

    // 添加原始文本中的其他部分
    const headerLines = []
    const footerLines = []
    let inHeader = true

    for (const line of lines) {
      if (line.startsWith('模板名称：')) {
        inHeader = false
      } else if (line.startsWith('请根据以上评估结果')) {
        inHeader = false
        footerLines.push(line)
      } else if (inHeader) {
        headerLines.push(line)
      } else if (line.startsWith('请根据以上评估结果')) {
        footerLines.push(line)
      }
    }

    const result = headerLines.join('\n') + convertedText + '\n' + footerLines.join('\n')
    return result

  } catch (error) {
    console.error('转换表单数据失败:', error)
    return formDataStr
  }
}

// 生成AI分析
const generateAiAnalysis = async () => {
  // 检查listExecutionInfo是否存在且id是否有效
  if (!props.listExecutionInfo || !props.listExecutionInfo.id || isNaN(Number(props.listExecutionInfo.id))) {
    ElMessage.error('评估清单执行ID不能为空或无效')
    return
  }

  try {
    generatingAI.value = true

    // 清空之前的AI分析结果
    aiReasoning.value = ''
    aiAnalysis.value = ''
    tempReasoning.value = ''
    tempAnalysis.value = ''
    isAnalysisFinish.value = false

    // 准备AI输入数据
    const aiInputData = await prepareAiInputData()
    console.log('准备好的AI输入数据:', aiInputData)

    if (!aiInputData || aiInputData.trim() === '') {
      ElMessage.error('生成AI输入数据失败，请重试')
      generatingAI.value = false
      return
    }

    // 重新构建综合数据
    console.log('重新构建综合数据...')
    let finalAiInputData = aiInputData
    try {
      // 重新调用prepareAiInputData获取综合数据
      finalAiInputData = await prepareAiInputData()
      console.log('重新构建的综合数据:', finalAiInputData)
    } catch (error) {
      console.error('重新构建综合数据失败:', error)
    }

    aiInputs.value = finalAiInputData

    // 先保存AI输入数据到数据库
    try {
      // 获取当前登录用户ID作为评估师ID
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
      const evaluatorId = userInfo.id || props.listExecutionInfo.evaluatorId || props.listExecutionInfo.createBy

      if (!evaluatorId) {
        console.warn('未找到评估师ID，将使用默认值')
      }

      // 获取评估师姓名
      const evaluatorName = userInfo.nickname || props.listExecutionInfo.evaluatorName || '未知评估师'

      // 准备完整的更新数据（排除listRule避免重复计算分数）
      const updateData = {
        id: Number(props.listExecutionInfo.id),
        listId: props.listExecutionInfo.listId,
        listName: props.listExecutionInfo.listName,
        elderId: props.listExecutionInfo.elderId,
        elderName: props.listExecutionInfo.elderName,
        evaluatorId: evaluatorId || 1,
        evaluatorName: evaluatorName,
        status: props.listExecutionInfo.status,
        startTime: props.listExecutionInfo.startTime,
        requiredTemplateIds: props.listExecutionInfo.requiredTemplateIds,
        completedTemplateIds: props.listExecutionInfo.completedTemplateIds,
        draftTemplateIds: props.listExecutionInfo.draftTemplateIds,
        resultIds: props.listExecutionInfo.resultIds,
        aiInputs: finalAiInputData,
        evaluationReason: props.listExecutionInfo.evaluationReason || '',
        apiKey: props.listExecutionInfo.apiKey || ''
        // 注意：不传递listRule，避免重复计算分数
      }

      const updateResponse = await ListExecutionApi.updateListExecution(updateData)
      console.log('已保存AI输入数据到数据库，响应:', updateResponse)

      // 更新本地数据
      if (props.listExecutionInfo) {
        const updatedInfo = {
          ...props.listExecutionInfo,
          aiInputs: finalAiInputData,
          evaluatorId: evaluatorId || 1,
          evaluatorName: evaluatorName
        }
        emit('update:listExecutionInfo', updatedInfo)
      }
    } catch (error) {
      console.error('保存AI输入数据失败:', error)
      ElMessage.warning('保存AI输入数据失败，但将继续尝试生成AI分析')
    }

    // 显示AI分析对话框
    dialogVisible.value = true

    // 重置AI分析状态
    tempReasoning.value = ''
    tempAnalysis.value = ''
    isAnalysisFinish.value = false

    // 如果有正在进行的请求，则中断
    if (abortController) {
      abortController.abort()
    }

    // 创建新的 AbortController 实例
    abortController = new AbortController()

    // 调用AI分析接口
    await showAIResult(finalAiInputData)

    // 如果是直接从query参数传入的数据，也尝试重新构建综合数据
    if (props.directQuery) {
      try {
        console.log('检测到直接传入的query参数，尝试重新构建综合数据...')

        // 重新构建综合数据
        const newAiInputData = await prepareAiInputData()
        console.log('为直接query参数重新构建的综合数据:', newAiInputData)

        if (newAiInputData && newAiInputData.includes('各模板评估结果')) {
          // 保存转换后的数据
          aiInputs.value = newAiInputData

          // 更新本地数据
          const updatedInfo = {
            ...props.listExecutionInfo,
            aiInputs: newAiInputData
          }
          emit('update:listExecutionInfo', updatedInfo)

          // 重新调用AI分析接口
          await showAIResult(newAiInputData)
        }
      } catch (error) {
        console.error('处理直接传入的query参数失败:', error)
      }
    }
  } catch (error) {
    console.error('生成AI分析失败:', error)
    ElMessage.error('生成AI分析失败，请重试')
  } finally {
    generatingAI.value = false
  }
}

// 准备AI输入数据
const prepareAiInputData = async () => {
  console.log('开始准备AI输入数据，清单执行ID:', props.listExecutionInfo.id)

  try {
    // 直接使用API获取该清单执行下的所有评估结果
    const results = await ResultApi.getResultsByListExecutionId(props.listExecutionInfo.id)
    console.log('获取到评估清单执行的所有评估结果:', results)

    if (results && results.length > 0) {
      // 构建综合分析数据
      let aiInputData = '评估清单综合分析\n\n'
      aiInputData += `老人信息：${props.listExecutionInfo.elderName || '未知'}\n`
      aiInputData += `评估清单：${props.listExecutionInfo.listName || '未知'}\n\n`
      aiInputData += '各模板评估结果：\n'

      // 按模板名称排序，确保结果顺序一致
      results.sort((a, b) => {
        if (a.templateName && b.templateName) {
          return a.templateName.localeCompare(b.templateName)
        }
        return 0
      })

      // 处理每个评估结果
      for (const resultData of results) {
        // 跳过状态为暂存的结果
        if (resultData.status === 2) continue

        aiInputData += `\n模板名称：${resultData.templateName || '未知模板'}\n`

        // 优先使用aiInputs字段（如果有）
        if (resultData.aiInputs) {
          // 提取aiInputs中的内容（去掉评估表标题行和开头的老人信息等）
          let aiInputs = resultData.aiInputs
          if (aiInputs.startsWith('评估表标题：')) {
            // 提取评估表内容，跳过标题行
            const lines = aiInputs.split('\n')
            // 跳过第一行（标题行）
            aiInputs = lines.slice(2).join('\n')
          }
          aiInputData += aiInputs + '\n'
          continue
        }

        // 如果没有aiInputs字段，尝试从result解析数据
        if (resultData.result) {
          try {
            const parsedResult = JSON.parse(resultData.result)

            // 优先使用aiInput字段
            if (parsedResult.fullResult?.aiInput) {
              // 去掉评估表标题行，因为我们已经有了模板名称
              let aiInput = parsedResult.fullResult.aiInput
              if (aiInput.startsWith('评估表标题：')) {
                aiInput = aiInput.split('\n').slice(2).join('\n')
              }
              aiInputData += aiInput + '\n'
            }
            // 其次使用层次化结果
            else if (parsedResult.hierarchicalResults || parsedResult.fullResult?.hierarchicalResults) {
              const hierarchicalResults = parsedResult.hierarchicalResults || parsedResult.fullResult?.hierarchicalResults

              // 遍历每个顶级分类
              for (const category in hierarchicalResults) {
                const categoryData = hierarchicalResults[category]
                aiInputData += `${category}：\n`

                // 遍历该分类下的所有字段
                if (categoryData.children && categoryData.children.length > 0) {
                  categoryData.children.forEach(field => {
                    if (field.title && field.displayValue !== undefined) {
                      aiInputData += `  ${field.title}：${field.displayValue}\n`
                    }
                  })
                }
              }
            }

            // 如果有评分结果
            if (parsedResult.assessmentTemplateRuleResult) {
              const scoreResult = parsedResult.assessmentTemplateRuleResult
              aiInputData += `评分结果：${scoreResult.totalScore || '无'}\n`
              aiInputData += `评估结论：${scoreResult.result || '无'}\n`
            }

            // 添加评估师分析
            if (resultData.evaluatorAnalysis) {
              aiInputData += `评估师分析：${resultData.evaluatorAnalysis}\n`
            }

            // 添加AI分析
            if (resultData.aiAnalysis) {
              let cleanAiAnalysis = resultData.aiAnalysis
              if (cleanAiAnalysis.includes('[think]') && cleanAiAnalysis.includes('[/think]')) {
                cleanAiAnalysis = cleanAiAnalysis.split('[/think]')[1].trim()
              }
              aiInputData += `AI分析：${cleanAiAnalysis}\n`
            }
          } catch (error) {
            console.error(`解析评估结果${resultData.id}数据失败:`, error)
            aiInputData += `评估结果：无法解析\n`
          }
        } else {
          aiInputData += `评估结果：无数据\n`
        }
      }

      aiInputData += '\n请根据以上评估结果，给出综合分析意见，包括老人的整体状况、需要关注的问题、建议的照护措施等。'

      console.log('构建的AI输入数据:', aiInputData)

      // 保存构建的数据到数据库
      try {
        // 获取当前登录用户ID作为评估师ID
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
        const evaluatorId = userInfo.id || props.listExecutionInfo.evaluatorId || props.listExecutionInfo.createBy

        if (!evaluatorId) {
          console.warn('未找到评估师ID，将使用默认值')
        }

        // 获取评估师姓名
        const evaluatorName = userInfo.nickname || props.listExecutionInfo.evaluatorName || '未知评估师'

        // 准备完整的更新数据（排除listRule避免重复计算分数）
        const updateData = {
          id: Number(props.listExecutionInfo.id),
          listId: props.listExecutionInfo.listId,
          listName: props.listExecutionInfo.listName,
          elderId: props.listExecutionInfo.elderId,
          elderName: props.listExecutionInfo.elderName,
          evaluatorId: evaluatorId || 1,
          evaluatorName: evaluatorName,
          status: props.listExecutionInfo.status,
          startTime: props.listExecutionInfo.startTime,
          requiredTemplateIds: props.listExecutionInfo.requiredTemplateIds,
          completedTemplateIds: props.listExecutionInfo.completedTemplateIds,
          draftTemplateIds: props.listExecutionInfo.draftTemplateIds,
          resultIds: props.listExecutionInfo.resultIds,
          aiInputs: aiInputData,
          evaluationReason: props.listExecutionInfo.evaluationReason || '',
          apiKey: props.listExecutionInfo.apiKey || ''
          // 注意：不传递listRule，避免重复计算分数
        }

        await ListExecutionApi.updateListExecution(updateData)
        console.log('已保存构建的AI输入数据到数据库')

        // 更新本地数据
        const updatedInfo = {
          ...props.listExecutionInfo,
          aiInputs: aiInputData,
          evaluatorId: evaluatorId || 1,
          evaluatorName: evaluatorName
        }
        emit('update:listExecutionInfo', updatedInfo)
      } catch (error) {
        console.error('保存构建的AI输入数据失败:', error)
      }

      return aiInputData
    } else {
      const defaultInput = '评估清单综合分析\n\n未找到已完成的评估模板\n\n请根据以上评估结果，给出综合分析意见，包括老人的整体状况、需要关注的问题、建议的照护措施等。'
      console.log('未找到评估结果，使用默认输入:', defaultInput)
      return defaultInput
    }
  } catch (error) {
    console.error('获取评估清单执行的评估结果失败:', error)
    const errorInput = '评估清单综合分析\n\n获取评估结果失败，请检查系统日志\n\n请根据以上评估结果，给出综合分析意见，包括老人的整体状况、需要关注的问题、建议的照护措施等。'
    return errorInput
  }
}

// 显示AI分析结果
const showAIResult = async (aiInputData: string) => {
  // 清空所有AI分析相关变量
  isAnalysisFinish.value = false
  aiReasoning.value = ''
  aiAnalysis.value = ''
  tempReasoning.value = ''
  tempAnalysis.value = ''
  let currentContent = ''

  try {
    // 检查是否选择了AI模型
    if (!selectedModelId.value) {
      ElMessage.error('请先选择AI模型')
      return
    }

    // 确保aiInputData不为空
    if (!aiInputData || aiInputData.trim() === '') {
      ElMessage.error('AI输入数据为空，无法生成AI分析')
      return
    }

    // 打印请求参数，便于调试
    console.log('AI分析请求参数:', {
      modelId: selectedModelId.value,
      content: aiInputData,
      stream: true
    })

    // 检查aiInputData是否包含模板信息
    if (!aiInputData.includes('模板名称：')) {
      console.warn('警告: AI输入数据中可能缺少模板信息')
    }

    // 检查aiInputData是否包含原始表单数据格式或只有单个模板数据
    if (aiInputData.includes('F8xkm7j6fw3zb8c') || aiInputData.includes('Ffdrm7j66dn9alc') ||
        !aiInputData.includes('各模板评估结果') || aiInputData.split('模板名称').length < 3) {
      console.warn('警告: AI输入数据可能不完整或包含原始表单字段代码，尝试重新构建综合数据')
      ElMessage.warning('表单数据不完整，尝试重新获取所有模板数据...')

      // 重新构建综合数据
      try {
        // 重新调用prepareAiInputData获取综合数据
        const newAiInputData = await prepareAiInputData()
        console.log('重新构建的综合数据:', newAiInputData)

        if (newAiInputData && newAiInputData.includes('各模板评估结果') && newAiInputData.split('模板名称').length >= 3) {
          aiInputData = newAiInputData

          // 更新本地状态
          aiInputs.value = newAiInputData
        } else {
          console.warn('重新构建的数据仍然不完整，继续使用原始数据')
        }
      } catch (error) {
        console.error('在showAIResult中重新构建综合数据失败:', error)
      }
    }

    dialogVisible.value = true

    // 如果有正在进行的请求，则中断
    if (abortController) {
      abortController.abort()
    }

    // 创建新的 AbortController 实例
    abortController = new AbortController()

    // 使用后端AI分析API
    await AiEvaluationApi.generateAnalysisStream({
      data: {
        modelId: selectedModelId.value,
        content: aiInputData,
        stream: true
      },
      onMessage: (res) => {
        try {
          console.log('收到AI响应:', res)
          const data = JSON.parse(res.data)
          console.log('解析后的数据:', data)

          if (data.code === 0 && data.data) {
            const content = data.data.content || ''
            console.log('AI内容片段:', content)

            if (content) {
              currentContent += content

              // 检查是否包含 </think> 标签
              if (currentContent.includes('</think>')) {
                const parts = currentContent.split('</think>')
                if (parts.length === 2) {
                  // 提取 think 标签中的内容
                  tempReasoning.value = parts[0].replace('<think>', '').trim()
                  // 提取 think 标签后的内容
                  tempAnalysis.value = parts[1].trim()
                }
              } else if (currentContent.includes('<think>')) {
                // 如果包含开始标签但还没有结束标签，将内容放入分析过程
                tempReasoning.value = currentContent.replace('<think>', '').trim()
                tempAnalysis.value = ''
              } else {
                // 如果没有think标签，将所有内容作为分析结果
                tempReasoning.value = ''
                tempAnalysis.value = currentContent.trim()
              }
            }

            // 检查是否完成 - 检查多种可能的完成标志
            const isFinished = data.data.finished || data.data.finish || data.finished || data.finish
            console.log('检查完成状态:', {
              'data.data.finished': data.data.finished,
              'data.data.finish': data.data.finish,
              'data.finished': data.finished,
              'data.finish': data.finish,
              'isFinished': isFinished,
              'currentContentLength': currentContent.length
            })

            if (isFinished) {
              isAnalysisFinish.value = true
              // 在生成完成时更新实际显示的内容
              aiReasoning.value = tempReasoning.value
              aiAnalysis.value = tempAnalysis.value
              // 分析完成后自动折叠，使用自定义折叠逻辑
              isReasoningCollapsed.value = ['1']

              // 调试信息
              console.log('AI分析完成:', {
                isAnalysisFinish: isAnalysisFinish.value,
                aiReasoning: aiReasoning.value,
                aiAnalysis: aiAnalysis.value,
                tempReasoning: tempReasoning.value,
                tempAnalysis: tempAnalysis.value
              })

              // AI 分析完成后弹出提示
              setTimeout(async () => {
                try {
                  await ElMessageBox.confirm('AI 分析已完成，是否保存？', '确认保存', {
                    confirmButtonText: '是',
                    cancelButtonText: '否',
                    type: 'warning'
                  })
                  // 用户点击了"是"，保存AI分析
                  await saveAIAnalysis()
                } catch {
                  // 用户点击了"否"，保持模态框打开
                  dialogVisible.value = true
                }
              }, 100)
            }
          }
        } catch (error) {
          console.error('解析AI分析响应失败:', error, 'raw response:', res)
        }
      },
      onError: (error) => {
        console.error('AI分析请求失败:', error)
        ElMessage.error('AI分析请求失败')
        dialogVisible.value = false
      },
      onClose: () => {
        console.log('AI分析请求结束')

        // 兜底处理：如果连接关闭时还有内容但没有标记为完成，手动标记为完成
        if (currentContent && !isAnalysisFinish.value) {
          console.log('连接关闭时发现未完成的内容，手动标记为完成:', currentContent)

          // 处理内容分类
          if (currentContent.includes('</think>')) {
            const parts = currentContent.split('</think>')
            if (parts.length === 2) {
              tempReasoning.value = parts[0].replace('<think>', '').trim()
              tempAnalysis.value = parts[1].trim()
            }
          } else if (currentContent.includes('<think>')) {
            tempReasoning.value = currentContent.replace('<think>', '').trim()
            tempAnalysis.value = ''
          } else {
            tempReasoning.value = ''
            tempAnalysis.value = currentContent.trim()
          }

          // 标记为完成
          isAnalysisFinish.value = true
          aiReasoning.value = tempReasoning.value
          aiAnalysis.value = tempAnalysis.value
          isReasoningCollapsed.value = ['1']

          console.log('兜底处理完成:', {
            isAnalysisFinish: isAnalysisFinish.value,
            aiReasoning: aiReasoning.value,
            aiAnalysis: aiAnalysis.value
          })

          // 弹出保存提示
          setTimeout(async () => {
            try {
              await ElMessageBox.confirm('AI 分析已完成，是否保存？', '确认保存', {
                confirmButtonText: '是',
                cancelButtonText: '否',
                type: 'warning'
              })
              await saveAIAnalysis()
            } catch {
              dialogVisible.value = true
            }
          }, 100)
        }
      },
      ctrl: abortController
    })
  } catch (error: any) {
    if (error.name === 'AbortError') {
      ElMessage.warning('请求被中断')
    } else {
      console.error('获取 AI 结果失败:', error)
      ElMessage.error(error.message || '获取AI结果失败')
      dialogVisible.value = false
    }
  } finally {
    abortController = null // 清除 AbortController
  }
}

// 保存AI分析
const saveAIAnalysis = async () => {
  // 检查是否有AI分析内容（分析过程或分析结果）
  const hasAnalysisContent = (aiAnalysis.value && aiAnalysis.value.trim()) || (aiReasoning.value && aiReasoning.value.trim())

  console.log('保存AI分析检查:', {
    aiAnalysis: aiAnalysis.value,
    aiReasoning: aiReasoning.value,
    isAnalysisFinish: isAnalysisFinish.value,
    hasAnalysisContent: hasAnalysisContent,
    aiAnalysisLength: aiAnalysis.value ? aiAnalysis.value.length : 0,
    aiReasoningLength: aiReasoning.value ? aiReasoning.value.length : 0
  })

  if (!hasAnalysisContent) {
    ElMessage.warning('暂无AI分析内容，请先生成AI分析')
    return
  }

  // 检查listExecutionInfo是否存在且id是否有效
  if (!props.listExecutionInfo || !props.listExecutionInfo.id || isNaN(Number(props.listExecutionInfo.id))) {
    ElMessage.error('评估清单执行ID不能为空或无效')
    return
  }

  try {
    saving.value = true

    // 获取当前登录用户ID作为评估师ID
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    const evaluatorId = userInfo.id || props.listExecutionInfo.evaluatorId || props.listExecutionInfo.createBy

    if (!evaluatorId) {
      console.warn('未找到评估师ID，将使用默认值')
    }

    // 获取评估师姓名
    const evaluatorName = userInfo.nickname || props.listExecutionInfo.evaluatorName || '未知评估师'

    // 准备完整的更新数据（排除listRule避免重复计算分数）
    const updateData = {
      id: Number(props.listExecutionInfo.id),
      listId: props.listExecutionInfo.listId,
      listName: props.listExecutionInfo.listName,
      elderId: props.listExecutionInfo.elderId,
      elderName: props.listExecutionInfo.elderName,
      evaluatorId: evaluatorId || 1,
      evaluatorName: evaluatorName,
      status: props.listExecutionInfo.status,
      startTime: props.listExecutionInfo.startTime,
      requiredTemplateIds: props.listExecutionInfo.requiredTemplateIds,
      completedTemplateIds: props.listExecutionInfo.completedTemplateIds,
      draftTemplateIds: props.listExecutionInfo.draftTemplateIds,
      resultIds: props.listExecutionInfo.resultIds,
      aiInputs: aiInputs.value,
      // 保存AI分析结果，包含分析过程和分析结果
      aiAnalysis: aiReasoning.value ? `[think]${aiReasoning.value}[/think]\n${aiAnalysis.value}` : aiAnalysis.value,
      evaluationReason: props.listExecutionInfo.evaluationReason || '',
      apiKey: props.listExecutionInfo.apiKey || ''
      // 注意：不传递listRule，避免重复计算分数
    }

    // 调用API更新AI分析
    await ListExecutionApi.updateListExecution(updateData)

    // 更新本地数据，包含AI分析结果
    const updatedInfo = {
      ...props.listExecutionInfo,
      aiInputs: aiInputs.value,
      // 更新aiAnalysis字段，保存AI分析结果
      aiAnalysis: aiReasoning.value ? `[think]${aiReasoning.value}[/think]\n${aiAnalysis.value}` : aiAnalysis.value,
      evaluatorId: evaluatorId || 1,
      evaluatorName: evaluatorName
    }
    emit('update:listExecutionInfo', updatedInfo)
    emit('saved')

    ElMessage.success('AI综合分析保存成功')
    dialogVisible.value = false
  } catch (error) {
    console.error('保存AI分析失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

// 格式化AI分析内容为HTML
const formattedAIAnalysis = computed(() => {
  if (!aiAnalysis.value) return ''
  return marked(aiAnalysis.value)
})

// 提交整个评估清单
const submitEvaluationList = async () => {
  // 检查listExecutionInfo是否存在且id是否有效
  if (!props.listExecutionInfo || !props.listExecutionInfo.id || isNaN(Number(props.listExecutionInfo.id))) {
    ElMessage.error('评估清单执行ID不能为空或无效')
    return
  }

  try {
    saving.value = true

    // 获取当前登录用户ID作为评估师ID
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
    const evaluatorId = userInfo.id || props.listExecutionInfo.evaluatorId || props.listExecutionInfo.createBy

    if (!evaluatorId) {
      console.warn('未找到评估师ID，将使用默认值')
    }

    // 获取评估师姓名
    const evaluatorName = userInfo.nickname || props.listExecutionInfo.evaluatorName || '未知评估师'

    // 确认提交
    await ElMessageBox.confirm(
      '提交后将完成整个评估清单，确定要提交吗？',
      '提交确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 准备完整的更新数据（排除listRule避免重复计算分数）
    const updateData = {
      id: Number(props.listExecutionInfo.id),
      listId: props.listExecutionInfo.listId,
      listName: props.listExecutionInfo.listName,
      elderId: props.listExecutionInfo.elderId,
      elderName: props.listExecutionInfo.elderName,
      evaluatorId: evaluatorId || 1,
      evaluatorName: evaluatorName,
      status: 1, // 设置状态为已完成(1)
      startTime: props.listExecutionInfo.startTime,
      requiredTemplateIds: props.listExecutionInfo.requiredTemplateIds,
      completedTemplateIds: props.listExecutionInfo.requiredTemplateIds, // 所有模板都标记为已完成
      draftTemplateIds: '', // 清空草稿模板
      resultIds: props.listExecutionInfo.resultIds,
      evaluatorAnalysis: evaluatorAnalysis.value,
      aiInputs: props.listExecutionInfo.aiInputs || '',
      aiAnalysis: aiAnalysis.value || '',
      evaluationReason: props.listExecutionInfo.evaluationReason || '',
      apiKey: props.listExecutionInfo.apiKey || ''
      // 注意：不传递listRule，避免重复计算分数
    }

    // 调用API更新评估清单状态
    await ListExecutionApi.updateListExecution(updateData)

    // 更新本地数据
    const updatedInfo = {
      ...props.listExecutionInfo,
      status: 1,
      completedTemplateIds: props.listExecutionInfo.requiredTemplateIds,
      draftTemplateIds: '',
      evaluatorAnalysis: evaluatorAnalysis.value,
      aiAnalysis: aiAnalysis.value || '',
      evaluatorId: evaluatorId || 1,
      evaluatorName: evaluatorName
    }
    emit('update:listExecutionInfo', updatedInfo)
    emit('saved')

    ElMessage.success('评估清单提交成功')

    // 在localStorage中设置标记，表示需要刷新评估记录页面
    localStorage.setItem('evaluation_result_refresh', 'true')
    localStorage.setItem('evaluation_result_timestamp', new Date().getTime().toString())

    // 如果是从其他页面打开的，则关闭当前页面并返回上一页
    if (window.opener) {
      // 如果是从其他窗口打开的，关闭当前窗口
      window.close()
    } else {
      // 触发父组件的跳转函数
      emit('evaluationComplete')
    }
  } catch (error) {
    if (error === 'cancel') {
      console.log('用户取消了提交操作')
    } else {
      console.error('提交评估清单失败:', error)
      ElMessage.error('提交失败，请重试')
    }
  } finally {
    saving.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  convertFormDataToReadable,
  submitEvaluationList
})
</script>

<template>
  <div>
    <div class="summary-analysis-container">
      <el-card class="h-full">
        <template #header>
          <div class="flex justify-between items-center">
            <h3 class="m-0">清单综合分析</h3>
            <div class="flex gap-2">
              <el-button
                v-if="!readonly && !isEditing"
                type="primary"
                @click="toggleEdit"
              >
                编辑评估师分析
              </el-button>
              <el-button
                v-if="!readonly && isEditing"
                type="success"
                @click="saveEvaluation"
                :loading="saving"
              >
                保存
              </el-button>
              <el-button
                v-if="!readonly && isEditing"
                @click="toggleEdit"
              >
                取消
              </el-button>
              <!-- AI模型选择 -->
              <el-select
                v-if="!readonly"
                v-model="selectedModelId"
                placeholder="选择AI模型"
                style="width: 200px; margin-right: 8px"
                size="default"
              >
                <el-option
                  v-for="model in aiModelList"
                  :key="model.id"
                  :label="model.name"
                  :value="model.id"
                />
              </el-select>
              <el-button
                v-if="!readonly"
                type="primary"
                @click="generateAiAnalysis"
                :loading="generatingAI"
              >
                生成AI综合分析
              </el-button>
              <el-button
                v-if="!readonly"
                type="success"
                @click="submitEvaluationList"
                :loading="saving"
              >
                提交评估清单
              </el-button>
            </div>
          </div>
        </template>

        <div class="summary-content">
          <!-- 评估师分析 -->
          <div class="mb-4">
            <h4 class="text-lg font-medium mb-2">评估师综合分析</h4>
            <el-input
              v-if="isEditing"
              v-model="evaluatorAnalysis"
              type="textarea"
              :rows="6"
              placeholder="请输入评估师综合分析..."
            />
            <div v-else class="analysis-text">
              <div v-if="evaluatorAnalysis" class="whitespace-pre-wrap">{{ evaluatorAnalysis }}</div>
              <el-empty v-else description="暂无评估师综合分析" />
            </div>
          </div>

          <!-- AI分析 -->
          <div class="mt-6">
            <h4 class="text-lg font-medium mb-2">AI综合分析</h4>
            <div class="analysis-text">
              <div v-if="aiAnalysis" v-html="formattedAIAnalysis" class="markdown-content"></div>
              <el-empty v-else description="暂无AI综合分析" />
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- AI分析结果对话框 -->
    <el-dialog v-model="dialogVisible" title="AI 综合分析" width="70%">
      <div>
        <el-card class="custom-collapse-card reasoning-card" shadow="hover">
          <template #header>
            <div
              class="custom-collapse-header"
              @click="isReasoningCollapsed = isReasoningCollapsed.includes('1') ? [] : ['1']"
            >
              <div class="flex justify-between w-full pr-4">
                <div class="flex items-center">
                  <h3 class="m-0">分析过程</h3>
                  <el-tooltip content="AI思考的过程" placement="top" class="ml-2">
                    <el-icon class="ml-1">
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
                <div class="flex items-center">
                  <span class="text-sm text-gray-500 mr-2">
                    {{ isReasoningCollapsed.includes('1') ? '点击展开' : '点击折叠' }}
                  </span>
                  <el-icon
                    class="transition-all"
                    :class="isReasoningCollapsed.includes('1') ? 'transform rotate-180' : ''"
                  >
                    <ArrowDown />
                  </el-icon>
                </div>
              </div>
            </div>
          </template>
          <div
            class="custom-collapse-content"
            :class="{ hidden: isReasoningCollapsed.includes('1') }"
          >
            <div class="reasoning-container">
              <div
                class="text-wrap text-base whitespace-pre-wrap reasoning-content"
              >
                {{ isAnalysisFinish ? aiReasoning : tempReasoning }}
              </div>
            </div>
          </div>
        </el-card>

        <el-divider />

        <el-card class="result-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <div class="flex items-center">
                <h3>分析结果</h3>
                <el-tooltip content="AI分析的结论" placement="top" class="ml-2">
                  <el-icon class="ml-1">
                    <InfoFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </div>
          </template>
          <div class="result-container">
            <div
              v-html="marked(isAnalysisFinish ? aiAnalysis : tempAnalysis)"
              class="text-wrap leading-7 text-base result-content"
            ></div>
          </div>
        </el-card>
      </div>
      <template #footer>
        <div class="flex justify-between">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button
            type="primary"
            @click="saveAIAnalysis"
            :disabled="!isAnalysisFinish"
            :loading="saving"
          >
            保存分析结果
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.summary-analysis-container {
  height: 100%;
  width: 100%;

  :deep(.el-card__body) {
    overflow: hidden;
  }

  .summary-content {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    padding: 0 10px;
    width: 100%;
    overflow-x: hidden;
  }

  .analysis-text {
    min-height: 100px;
    padding: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #f5f7fa;
    word-break: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
    overflow-x: hidden;
  }

  :deep(.markdown-content) {
    max-width: 100%;
    word-break: break-word;
    overflow-wrap: break-word;

    h1, h2, h3, h4, h5, h6 {
      margin-top: 1em;
      margin-bottom: 0.5em;
      font-weight: 600;
      max-width: 100%;
    }

    p {
      margin-bottom: 1em;
      line-height: 1.6;
      max-width: 100%;
      word-break: break-word;
    }

    ul, ol {
      padding-left: 2em;
      margin-bottom: 1em;
      max-width: calc(100% - 2em);
    }

    li {
      margin-bottom: 0.5em;
      word-break: break-word;
    }

    blockquote {
      border-left: 4px solid #dfe2e5;
      padding-left: 1em;
      color: #6a737d;
      margin-bottom: 1em;
      max-width: calc(100% - 1em);
      word-break: break-word;
    }

    code {
      background-color: rgba(27, 31, 35, 0.05);
      border-radius: 3px;
      padding: 0.2em 0.4em;
      font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
      word-break: break-word;
      white-space: pre-wrap;
    }

    pre {
      background-color: #f6f8fa;
      border-radius: 3px;
      padding: 16px;
      overflow: auto;
      margin-bottom: 1em;
      max-width: 100%;
      white-space: pre-wrap;
    }
  }
}

// AI分析对话框样式
.custom-collapse-header {
  cursor: pointer;
  user-select: none;
}

.custom-collapse-content {
  transition: all 0.3s ease-in-out;
  overflow: hidden;

  &.hidden {
    max-height: 0;
    padding: 0;
    opacity: 0;
  }
}

.reasoning-card {
  margin-bottom: 16px;

  .reasoning-container {
    max-height: 300px;
    overflow-y: auto;
    padding: 12px;

    .reasoning-content {
      font-family: 'Courier New', Courier, monospace;
      white-space: pre-wrap;
      color: #476582;
      background-color: #f8f8f8;
      padding: 12px;
      border-radius: 4px;
      border-left: 4px solid #42b983;
    }
  }
}

.result-card {
  .result-container {
    max-height: 400px;
    overflow-y: auto;
    padding: 16px;

    .result-content {
      line-height: 1.6;
    }
  }
}
</style>
