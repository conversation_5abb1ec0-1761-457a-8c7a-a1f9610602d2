import request from '@/config/axios'

// 老人健康信息 VO
export interface HealthInfoVO {
  id: number // 健康信息ID
  elderId: number // 关联的老人ID
  pressureInjury: number // 压力性损伤评估
  jointActivity: number // 关节活动度评估
  woundCondition: string // 伤口情况
  specialCare: string // 特殊护理情况
  painAssessment: number // 疼痛感
  teethMissing: string // 牙齿缺失情况
  dentureWearing: string // 义齿佩戴情况
  swallowingDifficulty: number // 吞咽困难情况和症状
  nutritionStatus: boolean // 营养状况异常
  respiratoryFunction: boolean // 呼吸道功能异常
  consciousnessState: boolean // 意识状态异常
  otherConditions: string // 其他特殊情况
  assessmentDate: Date // 评估日期
  assessorName: string // 评估人员
  remark: string // 备注
}

// 老人健康信息 API
export const HealthInfoApi = {
  // 查询老人健康信息分页
  getHealthInfoPage: async (params: any) => {
    return await request.get({ url: `/elderArchives/health-info/page`, params })
  },

  // 查询老人健康信息详情
  getHealthInfo: async (id: number) => {
    return await request.get({ url: `/elderArchives/health-info/get?id=` + id })
  },

  // 新增老人健康信息
  createHealthInfo: async (data: HealthInfoVO) => {
    return await request.post({ url: `/elderArchives/health-info/create`, data })
  },

  // 修改老人健康信息
  updateHealthInfo: async (data: HealthInfoVO) => {
    return await request.put({ url: `/elderArchives/health-info/update`, data })
  },

  // 删除老人健康信息
  deleteHealthInfo: async (id: number) => {
    return await request.delete({ url: `/elderArchives/health-info/delete?id=` + id })
  },

  // 导出老人健康信息 Excel
  exportHealthInfo: async (params) => {
    return await request.download({ url: `/elderArchives/health-info/export-excel`, params })
  },
}
