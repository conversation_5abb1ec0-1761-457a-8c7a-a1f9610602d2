import request from '@/config/axios'
import { ResultVO } from '../result'
import { ListExecutionVO } from '../listexecution'

// 评估应用暂存数据 VO
export interface EvaluationDraftVO {
  id?: number // 暂存ID
  elderId: number // 老人ID
  elderName: string // 老人姓名
  evaluatorId: number // 评估师ID
  evaluatorName: string // 评估师姓名
  templateId?: number // 模板ID，单模板评估时使用
  templateName?: string // 模板名称
  listId?: number // 评估任务清单ID，清单评估时使用
  listName?: string // 评估任务清单名称
  evaluationReason: string // 评估原因
  formData: string // 表单数据，JSON格式
  evaluatorAnalysis?: string // 评估师分析
  aiInputs?: string // AI输入
  aiAnalysis?: string // AI分析
  status: number // 状态：0-暂存，1-已提交
  createTime: Date // 创建时间
  updateTime: Date // 更新时间
}

// 评估应用 API
export const EvaluationApplicationApi = {
  // 获取评估模板列表（带筛选）
  getTemplateList: async (params: any) => {
    return await request.get({ url: `/evaluation/template/page`, params })
  },

  // 获取评估清单列表（带筛选）
  getListPage: async (params: any) => {
    return await request.get({ url: `/evaluation/list/page`, params })
  },

  // 获取评估模板详情
  getTemplateDetail: async (id: number) => {
    return await request.get({ url: `/evaluation/template/get?id=` + id })
  },

  // 获取评估清单详情
  getListDetail: async (id: number) => {
    return await request.get({ url: `/evaluation/list/get?id=` + id })
  },

  // 暂存评估数据（使用现有的评估结果API）
  saveDraft: async (data: ResultVO) => {
    // 设置type为暂存类型
    data.type = 2 // 假设2表示暂存状态
    return await request.post({ url: `/evaluation/result/create`, data })
  },

  // 更新暂存的评估数据
  updateDraft: async (data: ResultVO) => {
    return await request.put({ url: `/evaluation/result/update`, data })
  },

  // 获取暂存的评估数据列表
  getDraftList: async (params: any) => {
    // 添加type=2筛选条件，表示只获取暂存状态的数据
    params.type = 2
    return await request.get({ url: `/evaluation/result/page`, params })
  },

  // 获取暂存的评估数据详情
  getDraftDetail: async (id: number) => {
    return await request.get({ url: `/evaluation/result/get?id=` + id })
  },

  // 提交评估数据（单模板评估）
  submitEvaluation: async (data: ResultVO) => {
    // 设置type为正式提交类型
    data.type = 0 // 假设0表示正式评估
    return await request.post({ url: `/evaluation/result/create`, data })
  },

  // 提交评估数据（清单评估）
  submitListEvaluation: async (data: ListExecutionVO) => {
    return await request.post({ url: `/evaluation/list-execution/create`, data })
  },

  // 更新评估数据（清单评估）
  updateListEvaluation: async (data: ListExecutionVO) => {
    return await request.put({ url: `/evaluation/list-execution/update`, data })
  }
}
