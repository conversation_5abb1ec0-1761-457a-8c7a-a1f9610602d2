<template>
  <div>
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="140px"
      >
      <el-form-item label="老人姓名" prop="elderId">
        <el-select
          v-model="queryParams.elderId"
          placeholder="请选择老人姓名"
          clearable
          filterable
          class="!w-240px"
        >
          <el-option
            v-for="item in elderProfiles"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="压力性损伤评估" prop="pressureInjury">
        <el-select
          v-model="queryParams.pressureInjury"
          placeholder="请选择压力性损伤评估"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_PRESSURE_INJURY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关节活动度评估" prop="jointActivity">
        <el-select
          v-model="queryParams.jointActivity"
          placeholder="请选择关节活动度评估"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_JOINT_ACTIVITY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="伤口情况" prop="woundCondition">
        <el-select
          v-model="queryParams.woundCondition"
          placeholder="请选择伤口情况"
          clearable
          class="!w-240px"
          multiple
          :multiple-limit="5"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_WOUND_CONDITION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="特殊护理情况" prop="specialCare">
        <el-select
          v-model="queryParams.specialCare"
          placeholder="请选择特殊护理情况"
          clearable
          class="!w-240px"
          multiple
          :multiple-limit="5"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_SPECIAL_CARE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="疼痛感" prop="painAssessment">
        <el-select
          v-model="queryParams.painAssessment"
          placeholder="请选择疼痛感"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_PAIN_ASSESSMENT)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="牙齿缺失情况" prop="teethMissing">
        <el-select
          v-model="queryParams.teethMissing"
          placeholder="请选择牙齿缺失情况"
          clearable
          class="!w-240px"
          multiple
          :multiple-limit="5"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_TEETH_MISSING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="义齿佩戴情况" prop="dentureWearing">
        <el-select
          v-model="queryParams.dentureWearing"
          placeholder="请选择义齿佩戴情况"
          clearable
          class="!w-240px"
          multiple
          :multiple-limit="5"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_NTURE_WEARING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="吞咽困难情况和症状" prop="swallowingDifficulty">
        <el-select
          v-model="queryParams.swallowingDifficulty"
          placeholder="请选择吞咽困难情况和症状"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_HEALTH_INFO_SWALLOWING_DIFFICULTY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="营养状况异常" prop="nutritionStatus">
        <el-select
          v-model="queryParams.nutritionStatus"
          placeholder="请选择营养状况异常"
          clearable
          class="!w-240px"
        >
          <el-option :label="'是'" :value="true" />
          <el-option :label="'否'" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="呼吸道功能异常" prop="respiratoryFunction">
        <el-select
          v-model="queryParams.respiratoryFunction"
          placeholder="请选择呼吸道功能异常"
          clearable
          class="!w-240px"
        >
          <el-option :label="'是'" :value="true" />
          <el-option :label="'否'" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="意识状态异常" prop="consciousnessState">
        <el-select
          v-model="queryParams.consciousnessState"
          placeholder="请选择意识状态异常"
          clearable
          class="!w-240px"
        >
          <el-option :label="'是'" :value="true" />
          <el-option :label="'否'" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="其他特殊情况" prop="otherConditions">
        <el-input
          v-model="queryParams.otherConditions"
          placeholder="请输入其他特殊情况"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="评估日期" prop="assessmentDate">
        <el-date-picker
          v-model="queryParams.assessmentDate"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="评估人员" prop="assessorName">
        <el-input
          v-model="queryParams.assessorName"
          placeholder="请输入评估人员"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
<!--      <el-form-item label="备注" prop="remark">-->
<!--        <el-input-->
<!--          v-model="queryParams.remark"-->
<!--          placeholder="请输入备注"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['elderArchives:health-info:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['elderArchives:health-info:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
<!--      <el-table-column label="健康信息ID" align="center" prop="id" width="120" />-->
      <el-table-column label="老人姓名" align="center" prop="elderName" min-width="120">
        <template #default="scope">
          <el-text >{{ scope.row.elderName || '未知' }}</el-text>
        </template>
      </el-table-column>
      <el-table-column label="评估日期" align="center" prop="assessmentDate" width="120" :formatter="arrayDateFormatter" />
      <el-table-column label="评估人员" align="center" prop="assessorName" width="120" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180"
      />
      <el-table-column label="操作" align="center" min-width="180">
        <template #default="scope">
          <el-button
            link
            type="info"
            @click="openDetailDialog(scope.row)"
          >
            详情
          </el-button>
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['elderArchives:health-info:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['elderArchives:health-info:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <HealthInfoForm ref="formRef" @success="getList" />

  <!-- 详情对话框 -->
  <el-dialog
    v-model="detailDialogVisible"
    title="健康信息详情"
    width="80%"
    :close-on-click-modal="false"
  >
    <div v-if="currentHealthInfo" class="detail-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="健康信息ID">
          {{ currentHealthInfo.id }}
        </el-descriptions-item>
        <el-descriptions-item label="老人姓名">
          <el-text type="primary">{{ currentHealthInfo.elderName || '未知' }}</el-text>
        </el-descriptions-item>
        <el-descriptions-item label="压力性损伤评估">
          <dict-tag :type="DICT_TYPE.ELDER_HEALTH_INFO_PRESSURE_INJURY" :value="currentHealthInfo.pressureInjury" />
        </el-descriptions-item>
        <el-descriptions-item label="关节活动度评估">
          <dict-tag :type="DICT_TYPE.ELDER_HEALTH_INFO_JOINT_ACTIVITY" :value="currentHealthInfo.jointActivity" />
        </el-descriptions-item>
        <el-descriptions-item label="伤口情况">
          <dict-tag :type="DICT_TYPE.ELDER_HEALTH_INFO_WOUND_CONDITION" :value="currentHealthInfo.woundCondition" />
        </el-descriptions-item>
        <el-descriptions-item label="特殊护理情况">
          <dict-tag :type="DICT_TYPE.ELDER_HEALTH_INFO_SPECIAL_CARE" :value="currentHealthInfo.specialCare" />
        </el-descriptions-item>
        <el-descriptions-item label="疼痛感">
          <dict-tag :type="DICT_TYPE.ELDER_HEALTH_INFO_PAIN_ASSESSMENT" :value="currentHealthInfo.painAssessment" />
        </el-descriptions-item>
        <el-descriptions-item label="牙齿缺失情况">
          <dict-tag :type="DICT_TYPE.ELDER_HEALTH_INFO_TEETH_MISSING" :value="currentHealthInfo.teethMissing" />
        </el-descriptions-item>
        <el-descriptions-item label="义齿佩戴情况">
          <dict-tag :type="DICT_TYPE.ELDER_HEALTH_INFO_NTURE_WEARING" :value="currentHealthInfo.dentureWearing" />
        </el-descriptions-item>
        <el-descriptions-item label="吞咽困难情况和症状">
          <dict-tag :type="DICT_TYPE.ELDER_HEALTH_INFO_SWALLOWING_DIFFICULTY" :value="currentHealthInfo.swallowingDifficulty" />
        </el-descriptions-item>
        <el-descriptions-item label="营养状况异常">
          <el-tag :type="currentHealthInfo.nutritionStatus ? 'success' : 'info'">
            {{ currentHealthInfo.nutritionStatus ? '有' : '无' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="呼吸道功能异常">
          <el-tag :type="currentHealthInfo.respiratoryFunction ? 'success' : 'info'">
            {{ currentHealthInfo.respiratoryFunction ? '有' : '无' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="意识状态异常">
          <el-tag :type="currentHealthInfo.consciousnessState ? 'success' : 'info'">
            {{ currentHealthInfo.consciousnessState ? '有' : '无' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="其他特殊情况" :span="2">
          {{ currentHealthInfo.otherConditions || '无' }}
        </el-descriptions-item>
        <el-descriptions-item label="评估日期">
          {{ formatArrayDate(currentHealthInfo.assessmentDate) }}
        </el-descriptions-item>
        <el-descriptions-item label="评估人员">
          {{ currentHealthInfo.assessorName }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          {{ currentHealthInfo.remark || '无' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ dateFormatter(currentHealthInfo.createTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ dateFormatter(currentHealthInfo.updateTime) }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="detailDialogVisible = false">关闭</el-button>
      <el-button
        type="primary"
        @click="openForm('update', currentHealthInfo?.id)"
        v-hasPermi="['elderArchives:health-info:update']"
      >
        编辑
      </el-button>
    </template>
  </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter, arrayDateFormatter, formatArrayDate } from '@/utils/formatTime'
import download from '@/utils/download'
import { HealthInfoApi, HealthInfoVO } from '@/api/elderArchives/healthinfo'
import { ArchivesProfileApi, ArchivesProfileSimpleVO } from '@/api/elderArchives/archivesProfile'
import HealthInfoForm from './HealthInfoForm.vue'

/** 老人健康信息 列表 */
defineOptions({ name: 'HealthInfo' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 扩展HealthInfoVO类型，添加elderName属性
interface HealthInfoWithElderName extends HealthInfoVO {
  elderName?: string
}

const loading = ref(true) // 列表的加载中
const list = ref<HealthInfoWithElderName[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const elderProfiles = ref<ArchivesProfileSimpleVO[]>([]) // 老人列表
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  elderId: undefined,
  pressureInjury: undefined,
  jointActivity: undefined,
  woundCondition: [],
  specialCare: [],
  painAssessment: undefined,
  teethMissing: [],
  dentureWearing: [],
  swallowingDifficulty: undefined,
  nutritionStatus: undefined,
  respiratoryFunction: undefined,
  consciousnessState: undefined,
  otherConditions: undefined,
  assessmentDate: [],
  assessorName: undefined,
  remark: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 详情对话框相关
const detailDialogVisible = ref(false) // 详情对话框显示状态
const currentHealthInfo = ref<HealthInfoWithElderName | null>(null) // 当前查看的健康信息

/** 处理查询参数 - 将多选字段的数组转换为字符串 */
const formatQueryParams = (params: any) => {
  const formattedParams = { ...params }

  // 需要转换的多选字段
  const multiSelectFields = ['woundCondition', 'specialCare', 'teethMissing', 'dentureWearing']

  multiSelectFields.forEach(field => {
    const fieldValue = formattedParams[field]
    if (Array.isArray(fieldValue) && fieldValue.length > 0) {
      // 将数组转换为逗号分隔的字符串
      formattedParams[field] = fieldValue.join(',')
    } else if (Array.isArray(fieldValue) && fieldValue.length === 0) {
      // 空数组设置为undefined，避免发送空数组
      formattedParams[field] = undefined
    }
  })

  return formattedParams
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 格式化查询参数
    const formattedParams = formatQueryParams(queryParams)
    const data = await HealthInfoApi.getHealthInfoPage(formattedParams)
    list.value = data.list
    total.value = data.total

    // 为每个健康信息记录添加老人姓名
    if (elderProfiles.value.length > 0) {
      list.value.forEach(item => {
        const elder = elderProfiles.value.find(profile => profile.id === item.elderId)
        item.elderName = elder ? elder.name : '未知'
      })
    }
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()

  // 确保多选字段重置为空数组格式
  queryParams.woundCondition = []
  queryParams.specialCare = []
  queryParams.teethMissing = []
  queryParams.dentureWearing = []

  handleQuery()
}

/** 获取老人列表 */
const getElderProfiles = async () => {
  try {
    elderProfiles.value = await ArchivesProfileApi.getArchivesProfileSimpleList()
  } catch (error) {
    console.error('获取老人列表失败', error)
  }
}

/** 打开详情对话框 */
const openDetailDialog = (row: HealthInfoWithElderName) => {
  currentHealthInfo.value = row
  detailDialogVisible.value = true
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await HealthInfoApi.deleteHealthInfo(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    // 格式化查询参数
    const formattedParams = formatQueryParams(queryParams)
    const data = await HealthInfoApi.exportHealthInfo(formattedParams)
    download.excel(data, '老人健康信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  await getElderProfiles()
  await getList()
})
</script>

<style scoped>
.detail-content {
  padding: 20px 0;
}

.detail-content :deep(.el-descriptions__label) {
  font-weight: 600;
  color: #606266;
  width: 150px;
}

.detail-content :deep(.el-descriptions__content) {
  color: #303133;
}

.detail-content :deep(.el-descriptions-item__cell) {
  padding: 12px 0;
}
</style>
