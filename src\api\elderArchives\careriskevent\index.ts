import request from '@/config/axios'

// 老人照护风险事件 VO
export interface CareRiskEventVO {
  id: number // 风险事件ID
  elderId: number // 关联的老人ID
  eventType: number // 事件类型
  eventLevel: number // 事件等级
  recordDate: Date // 记录日期
  recorder: string // 记录人员
  description: string // 事件描述
}

// 老人照护风险事件 API
export const CareRiskEventApi = {
  // 查询老人照护风险事件分页
  getCareRiskEventPage: async (params: any) => {
    return await request.get({ url: `/elderArchives/care-risk-event/page`, params })
  },

  // 查询老人照护风险事件详情
  getCareRiskEvent: async (id: number) => {
    return await request.get({ url: `/elderArchives/care-risk-event/get?id=` + id })
  },

  // 新增老人照护风险事件
  createCareRiskEvent: async (data: CareRiskEventVO) => {
    return await request.post({ url: `/elderArchives/care-risk-event/create`, data })
  },

  // 修改老人照护风险事件
  updateCareRiskEvent: async (data: CareRiskEventVO) => {
    return await request.put({ url: `/elderArchives/care-risk-event/update`, data })
  },

  // 删除老人照护风险事件
  deleteCareRiskEvent: async (id: number) => {
    return await request.delete({ url: `/elderArchives/care-risk-event/delete?id=` + id })
  },

  // 导出老人照护风险事件 Excel
  exportCareRiskEvent: async (params) => {
    return await request.download({ url: `/elderArchives/care-risk-event/export-excel`, params })
  },
}
