import request from '@/config/axios'

// 评估结果 VO
export interface ResultVO {
  id?: number // 结果ID
  connectResultId?: number // 关联评估结果ID
  elderId: number // 老人ID
  elderName: string // 老人姓名
  templateId: number // 模板ID
  templateName: string // 模板名称
  evaluatorId: number // 评估师ID
  evaluatorName: string // 评估师姓名
  evaluationReason: string // 评估原因
  evaluationTime: Date | number // 评估时间
  // evaluationType?: string // 评估类型
  result: String // 评估结果
  type: number // 评估方式
  status: number // 状态：1-已提交，2-暂存
  evaluatorAnalysis: string // 评估师分析
  aiInputs: string // AI输入
  aiAnalysis: string // AI分析
  listExecutionId?: number // 评估清单执行ID
  totalScore?: number // 评估总分
  assessmentResult?: string // 评估结果文本
}

// 简单评估结果 VO
export interface ResultTemplateVO {
  id: number // 结果ID
  label: string // 模板名称
  evaluatorAnalysis: string // 评估师分析
  aiInputs: string // AI输入
  aiAnalysis: string // AI分析
}

// 更新评估师分析请求体
export interface EvaluatorAnalysisUpdateReqVO {
  id: number // 结果ID
  evaluatorAnalysis: string // 评估师分析
}

// 更新AI分析请求体
export interface AIAnalysisUpdateReqVO {
  id: number // 结果ID
  aiAnalysis: string // AI分析
}

// 评估结果 API
export const ResultApi = {
  // 查询评估结果分页
  getResultPage: async (params: any) => {
    return await request.get({ url: `/evaluation/result/page`, params })
  },

  // 查询评估结果分页（处理清单评估合并）
  getMergedResultPage: async (params: any) => {
    return await request.get({ url: `/evaluation/result/merged-page`, params })
  },

  // 查询评估结果详情
  getResult: async (id: number) => {
    return await request.get({ url: `/evaluation/result/get?id=` + id })
  },

  // 新增评估结果
  createResult: async (data: ResultVO) => {
    return await request.post({ url: `/evaluation/result/create`, data })
  },

  // 修改评估结果
  updateResult: async (data: ResultVO) => {
    return await request.put({ url: `/evaluation/result/update`, data })
  },

  // 更新评估师分析
  updateEvaluatorAnalysis: async (data: EvaluatorAnalysisUpdateReqVO) => {
    return await request.put({ url: `/evaluation/result/update-evaluator-analysis`, data })
  },

  // 更新AI分析
  updateAiAnalysis: async (data: AIAnalysisUpdateReqVO) => {
    return await request.put({ url: `/evaluation/result/update-ai-analysis`, data })
  },

  // 转换表单数据为可读文本
  convertFormData: async (data: { formData: string; formRule: string }) => {
    return await request.post({ url: `/evaluation/result/convert-form-data`, data })
  },

  // 删除评估结果
  deleteResult: async (id: number) => {
    return await request.delete({ url: `/evaluation/result/delete?id=` + id })
  },

  // 删除清单评估记录
  deleteListExecution: async (id: number) => {
    return await request.delete({ url: `/evaluation/result/delete-list-execution?id=` + id })
  },

  // 导出评估结果 Excel
  exportResult: async (params) => {
    return await request.download({ url: `/evaluation/result/export-excel`, params })
  },

  // 获取简单评估结果列表
  listSimple: async (elderId: number): Promise<ResultTemplateVO[]> => {
    return await request.get({
      url: '/evaluation/result/list-simple',
      params: { elderId }
    })
  },

  // 根据服务计划ID获取关联评估的模板名称
  selectEvaluationByPlanId: async (servicePlanId: number): Promise<string[]> => {
    return await request.get({
      url: `/evaluation/result/select-by-plan`,
      params: { servicePlanId }
    })
  },

  // 导出评估结果 Word
  exportResultWord: async (params) => {
    return await request.download({
      url: `/evaluation/result/export-word`,
      params
    })
  },

  // 导出清单评估结果 Word
  exportListExecutionWord: async (params) => {
    return await request.download({
      url: `/evaluation/result/export-list-word`,
      params
    })
  },

  // 批量获取评估AI输入内容
  getAiInputsByIds: async (ids: number[]): Promise<ResultTemplateVO[]> => {
    return await request.get({
      url: '/evaluation/result/get-ai-inputs',
      params: { ids: ids.join(',') }
    })
  },

  // 批量查询记录是否有复评
  getReEvaluationStatus: async (ids: number[]): Promise<{ [key: number]: boolean }> => {
    return await request.get({
      url: '/evaluation/result/re-evaluation-status',
      params: { ids: ids.join(',') }
    })
  },

  // 根据评估清单执行ID获取评估结果列表
  getResultsByListExecutionId: async (listExecutionId: number): Promise<ResultVO[]> => {
    return await request.get({
      url: '/evaluation/result/list-by-execution-id',
      params: { listExecutionId }
    })
  }
}
