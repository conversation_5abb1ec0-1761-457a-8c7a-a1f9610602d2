<template>
  <div class="template-selector">
    <!-- 搜索和筛选区域 -->
    <div class="filter-container">
      <el-form :model="queryParams" inline>
        <el-form-item label="模板名称">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入模板名称"
            clearable
            style="width: 180px;"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="模板类型">
          <el-select v-model="queryParams.type" placeholder="请选择模板类型" clearable style="width: 180px;">
            <el-option
              v-for="dict in templateTypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 180px;">
            <el-option
              v-for="dict in templateStatusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <Icon icon="ep:search" class="mr-5px" /> 搜索
          </el-button>
          <el-button @click="resetQuery">
            <Icon icon="ep:refresh" class="mr-5px" /> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 模板列表区域 -->
    <div class="template-list" v-loading="loading">
      <el-row :gutter="20">
        <el-col
          v-for="item in templateList"
          :key="item.id"
          :span="6"
          class="mb-20"
        >
          <el-card
            :class="['template-card', { 'is-selected': isSelected(item) }]"
            shadow="hover"
            @click="selectTemplate(item)"
          >
            <template #header>
              <div class="card-header">
                <el-tooltip :content="item.name" placement="top" :show-after="500">
                  <span class="template-name">{{ item.name }}</span>
                </el-tooltip>
                <div class="template-badges">
                  <el-tag size="small" :type="getTemplateTypeTag(item.type)" style="white-space: nowrap;">
                    {{ getTemplateTypeName(item.type) }}
                  </el-tag>
                  <el-tag
                    size="small"
                    :type="getTemplateStatusType(item.status)"
                    class="ml-5"
                    style="white-space: nowrap;"
                  >
                    {{ getTemplateStatusName(item.status) }}
                  </el-tag>
                </div>
              </div>
            </template>
            <div class="template-content">
              <div class="template-info">
                <p class="template-version">版本: {{ item.version || '无版本号' }}</p>
                <p class="template-time">创建时间: {{ formatDate(item.createTime) }}</p>
              </div>
              <div class="template-actions">
                <el-button
                  type="primary"
                  link
                  @click.stop="previewTemplate(item)"
                >
                  预览
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNo"
          :page-size="8"
          :total="total"
          layout="total, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 模板预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="模板预览"
      width="60%"
      destroy-on-close
      :close-on-click-modal="false"
      @closed="handlePreviewClose"
    >
      <div class="preview-form" v-if="previewTemplateData" v-loading="previewLoading">
        <div class="template-info">
          <h3>{{ previewTemplateData.name }}</h3>
          <div class="template-meta">
            <el-tag size="small" type="info" class="mr-10">版本: {{ previewTemplateData.version || '无版本号' }}</el-tag>
            <el-tag size="small" type="success" class="mr-10">类型: {{ getTemplateTypeName(previewTemplateData.type) }}</el-tag>
            <el-tag size="small" :type="getTemplateStatusType(previewTemplateData.status)">
              状态: {{ getTemplateStatusName(previewTemplateData.status) }}
            </el-tag>
          </div>

          <el-divider content-position="left">表单预览</el-divider>

          <form-create
            v-if="previewTemplateData.formSchema && templateRule.length > 0"
            v-model:value="previewForm"
            v-model:api="previewApi"
            :rule="templateRule"
            :option="templateOption"
          />
          <div v-else class="no-schema">
            <el-empty description="暂无表单结构或表单结构解析失败" />
          </div>
        </div>
      </div>
      <div v-else>
        <el-empty description="模板数据加载失败" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="previewDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineProps, defineExpose, watch } from 'vue'
import { TemplateApi } from '@/api/evaluation/template'
import { formatDate } from '@/utils/formatTime'
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { useMessage } from '@/hooks/web/useMessage'
import FormCreate from '@form-create/element-ui'

defineOptions({ name: 'TemplateSelector' })

const props = defineProps({
  selectedTemplate: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:selectedTemplate', 'template-selected'])

// 消息提示
const message = useMessage()

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 8, // 固定显示8个卡片（2行x4列）
  name: '',
  type: undefined,
  status: 0 // 默认只显示启用的模板
})

// 模板类型选项
const templateTypeOptions = ref(getIntDictOptions(DICT_TYPE.EVALUATION_TEMPLATE_TYPE))

// 模板状态选项
const templateStatusOptions = ref(getIntDictOptions(DICT_TYPE.EVALUATION_TEMPLATE_STATUS))

// 模板列表数据
const templateList = ref([])
const total = ref(0)
const loading = ref(false)

// 预览相关
const previewDialogVisible = ref(false)
const previewTemplateData = ref(null)
const previewLoading = ref(false)
const previewForm = ref({})
const previewApi = ref(null)
const templateRule = ref([])
const templateOption = ref({
  submitBtn: false,
  resetBtn: false,
  form: {
    labelWidth: '120px',
    labelPosition: 'right'
  }
})

// 判断模板是否被选中
const isSelected = (template: any) => {
  return props.selectedTemplate && props.selectedTemplate.id === template.id
}

// 选择模板
const selectTemplate = (template: any) => {
  emit('update:selectedTemplate', template)
  emit('template-selected', template)
}

// 获取模板类型标签样式
const getTemplateTypeTag = (type: number) => {
  const typeMap = {
    0: '',       // 数据收集表
    1: 'info',   // 数据分析表
    2: 'success' // AI分析表
  }
  return typeMap[type] || ''
}

// 获取模板类型名称
const getTemplateTypeName = (type: number) => {
  const item = templateTypeOptions.value.find((d: any) => d.value === type)
  return item ? item.label : '未知类型'
}

// 获取模板状态名称
const getTemplateStatusName = (status: number) => {
  const item = templateStatusOptions.value.find((d: any) => d.value === status)
  return item ? item.label : '未知状态'
}

// 获取模板状态标签类型
const getTemplateStatusType = (status: number) => {
  // 根据状态值返回对应的标签类型
  const statusMap = {
    0: 'success', // 启用
    1: 'danger'   // 禁用
  }
  return statusMap[status] || 'info'
}

// 格式化表单结构
const formatFormSchema = (schema: any) => {
  try {
    const obj = typeof schema === 'string' ? JSON.parse(schema) : schema
    return JSON.stringify(obj, null, 2)
  } catch (e) {
    return schema
  }
}

// 查询模板列表
const getTemplateList = async () => {
  loading.value = true
  try {
    const res = await TemplateApi.getTemplatePage(queryParams)
    templateList.value = res.list
    total.value = res.total
  } catch (error) {
    console.error('获取模板列表失败:', error)
    message.error('获取模板列表失败')
  } finally {
    loading.value = false
  }
}

// 预览模板
const previewTemplate = async (template: any) => {
  previewDialogVisible.value = true
  previewLoading.value = true

  try {
    const res = await TemplateApi.getTemplate(template.id)
    previewTemplateData.value = res

    // 解析模板数据
    if (res.formSchema) {
      try {
        const schema = JSON.parse(res.formSchema)
        templateRule.value = schema.rule || []
        // 可以选择是否使用保存的 option
        // templateOption.value = { ...templateOption.value, ...schema.option }
      } catch (error) {
        console.error('解析模板数据失败:', error)
        message.error('模板数据格式错误')
        templateRule.value = []
      }
    }
  } catch (error) {
    console.error('获取模板详情失败:', error)
    message.error('获取模板详情失败')
  } finally {
    previewLoading.value = false
  }
}

// 处理预览对话框关闭
const handlePreviewClose = () => {
  // 清空预览相关的数据
  previewTemplateData.value = null
  previewForm.value = {}
  previewApi.value = null
  templateRule.value = []
}

// 搜索
const handleQuery = () => {
  queryParams.pageNo = 1
  getTemplateList()
}

// 重置
const resetQuery = () => {
  queryParams.name = ''
  queryParams.type = undefined
  queryParams.status = 0
  handleQuery()
}

// 处理页码变化
const handleCurrentChange = (val: number) => {
  queryParams.pageNo = val
  getTemplateList()
}

// 暴露方法
defineExpose({
  getTemplateList,
  resetQuery
})

// 初始化
getTemplateList()
</script>

<style lang="scss" scoped>
.template-selector {
  .filter-container {
    margin-bottom: 20px;
  }

  .template-list {
    .mb-20 {
      margin-bottom: 20px;
    }

    .template-card {
      min-height: 220px; /* 增加最小高度，确保有足够空间显示内容 */
      cursor: pointer;
      transition: all 0.3s;
      border: 2px solid transparent;
      display: flex;
      flex-direction: column;
      width: 100%; /* 确保卡片宽度为100% */
      box-sizing: border-box; /* 确保边框不会增加宽度 */

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }

      &.is-selected {
        border-color: var(--el-color-primary);
      }

      .card-header {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .template-name {
          font-weight: bold;
          font-size: 16px;
          width: 100%;
          margin-bottom: 8px;
          /* 移除文本溢出省略 */
          /* overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap; */
          word-break: break-word; /* 允许在任何字符间换行 */
          line-height: 1.3;
        }

        .template-badges {
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap; /* 防止标签换行 */
          gap: 5px;
          width: 100%;
          justify-content: flex-start;
        }
      }

      .template-content {
        padding: 10px 0;
        flex: 1;
        display: flex;
        flex-direction: column;

        .template-info {
          margin-bottom: 10px;
          flex: 1;

          p {
            margin: 5px 0;
            color: #606266;
            font-size: 14px;
          }
        }

        .template-actions {
          display: flex;
          justify-content: flex-end;
          margin-top: auto;
        }
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .preview-form {
    padding: 20px;
    background: #f5f7fa;
    border-radius: 4px;

    .template-info {
      margin-bottom: 20px;

      h3 {
        margin-top: 0;
        margin-bottom: 15px;
      }

      .template-meta {
        margin-bottom: 15px;
      }

      .mr-10 {
        margin-right: 10px;
      }
    }

    :deep(.el-form) {
      max-width: 800px;
      margin: 0 auto;
    }

    .no-schema {
      padding: 20px;
      text-align: center;
    }
  }
}
</style>
