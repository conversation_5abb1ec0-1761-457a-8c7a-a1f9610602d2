<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="110px"
    >
      <el-form-item label="老人姓名" prop="elderId">
        <el-select
          v-model="queryParams.elderId"
          placeholder="请选择老人姓名"
          clearable
          filterable
          class="!w-240px"
        >
          <el-option
            v-for="item in elderProfiles"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="签约员工" prop="employeeIds">
        <el-select
          v-model="queryParams.employeeIds"
          placeholder="请选择签约员工"
          clearable
          filterable
          multiple
          collapse-tags
          collapse-tags-tooltip
          class="!w-240px"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.nickname"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="签约日期" prop="contractDate">
        <el-date-picker
          v-model="queryParams.contractDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="签约类型" prop="contractType">
        <el-select
          v-model="queryParams.contractType"
          placeholder="请选择签约类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_CONTRACT_INFO_CONTRACT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="签约原因" prop="contractReason">
        <el-input
          v-model="queryParams.contractReason"
          placeholder="请输入签约原因"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="签约期限(月)" prop="contractPeriod">
        <el-input
          v-model="queryParams.contractPeriod"
          placeholder="请输入签约期限(月)"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="开始日期" prop="startDate">
        <el-date-picker
          v-model="queryParams.startDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="结束日期" prop="endDate">
        <el-date-picker
          v-model="queryParams.endDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="签约状态" prop="contractStatus">
        <el-select
          v-model="queryParams.contractStatus"
          placeholder="请选择签约状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ELDER_CONTRACT_INFO_CONTRACT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="终止原因" prop="terminationReason">
        <el-input
          v-model="queryParams.terminationReason"
          placeholder="请输入终止原因"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
<!--      <el-form-item label="备注" prop="remark">-->
<!--        <el-input-->
<!--          v-model="queryParams.remark"-->
<!--          placeholder="请输入备注"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['elderArchives:contract-info:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['elderArchives:contract-info:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
<!--      <el-table-column label="签约信息ID" align="center" prop="id" />-->
      <el-table-column label="老人姓名" align="center" prop="elderName" min-width="100">
        <template #default="scope">
          <el-text >{{ scope.row.elderName || '未知' }}</el-text>
        </template>
      </el-table-column>
      <el-table-column label="签约员工" align="center" prop="employeeNames" min-width="120">
        <template #default="scope">
          <el-text type="primary">{{ scope.row.employeeNames || '未知' }}</el-text>
        </template>
      </el-table-column>
      <el-table-column label="签约日期" align="center" prop="contractDate" :formatter="dateArrayFormatter" />
      <el-table-column label="签约类型" align="center" prop="contractType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ELDER_CONTRACT_INFO_CONTRACT_TYPE" :value="scope.row.contractType" />
        </template>
      </el-table-column>
      <el-table-column label="签约原因" align="center" prop="contractReason" />
      <el-table-column label="签约期限(月)" align="center" prop="contractPeriod" />
      <el-table-column label="开始日期" align="center" prop="startDate" :formatter="dateArrayFormatter" />
      <el-table-column label="结束日期" align="center" prop="endDate" :formatter="dateArrayFormatter" />
      <el-table-column label="签约状态" align="center" prop="contractStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ELDER_CONTRACT_INFO_CONTRACT_STATUS" :value="scope.row.contractStatus" />
        </template>
      </el-table-column>
      <el-table-column label="终止原因" align="center" prop="terminationReason" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['elderArchives:contract-info:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['elderArchives:contract-info:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ContractInfoForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ContractInfoApi, ContractInfoVO } from '@/api/elderArchives/contractinfo'
import { ArchivesProfileApi, ArchivesProfileSimpleVO } from '@/api/elderArchives/archivesProfile'
import { getSimpleUserList, UserVO } from '@/api/system/user'
import ContractInfoForm from './ContractInfoForm.vue'

/** 老人签约信息 列表 */
defineOptions({ name: 'ContractInfo' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

// 扩展ContractInfoVO类型，添加elderName属性
interface ContractInfoWithElderName extends ContractInfoVO {
  elderName?: string
}

const loading = ref(true) // 列表的加载中
const list = ref<ContractInfoWithElderName[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  elderId: undefined,
  employeeIds: [], // 改为数组类型，支持多选
  contractDate: [],
  contractType: undefined,
  contractReason: undefined,
  contractPeriod: undefined,
  startDate: [],
  endDate: [],
  contractStatus: undefined,
  terminationReason: undefined,
  remark: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 老人列表
const elderProfiles = ref<ArchivesProfileSimpleVO[]>([])
// 员工列表
const userList = ref<UserVO[]>([])

/** 获取老人列表 */
const getElderProfiles = async () => {
  try {
    elderProfiles.value = await ArchivesProfileApi.getArchivesProfileSimpleList()
  } catch (error) {
    console.error('获取老人列表失败', error)
  }
}

/** 获取员工列表 */
const getUserList = async () => {
  try {
    userList.value = await getSimpleUserList()
  } catch (error) {
    console.error('获取员工列表失败', error)
  }
}

/** 日期数组格式化器：将 [year, month, day] 格式转换为 "YYYY-MM-DD" */
const dateArrayFormatter = (_row: any, _column: any, cellValue: any): string => {
  if (Array.isArray(cellValue) && cellValue.length === 3) {
    const [year, month, day] = cellValue
    return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
  }
  return cellValue || ''
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // 处理查询参数：将员工ID数组转换为字符串
    const searchParams = { ...queryParams }
    if (Array.isArray(searchParams.employeeIds) && searchParams.employeeIds.length > 0) {
      searchParams.employeeIds = searchParams.employeeIds.join(',')
    } else {
      searchParams.employeeIds = undefined
    }

    const data = await ContractInfoApi.getContractInfoPage(searchParams)
    list.value = data.list
    total.value = data.total

    // 为每个签约信息记录添加老人姓名和员工姓名
    if (elderProfiles.value.length > 0 || userList.value.length > 0) {
      list.value.forEach(item => {
        // 添加老人姓名
        if (elderProfiles.value.length > 0) {
          const elder = elderProfiles.value.find(profile => profile.id === item.elderId)
          item.elderName = elder ? elder.name : '未知'
        }

        // 添加员工姓名
        if (userList.value.length > 0 && item.employeeIds) {
          const employeeIdArray = item.employeeIds.split(',').filter(id => id.trim() !== '')
          const employeeNames = employeeIdArray.map(id => {
            const user = userList.value.find(user => user.id === parseInt(id.trim()))
            return user ? user.nickname : `未知(${id})`
          })
          item.employeeNames = employeeNames.join(', ')
        }
      })
    }
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ContractInfoApi.deleteContractInfo(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true

    // 处理导出参数：将员工ID数组转换为字符串
    const exportParams = { ...queryParams }
    if (Array.isArray(exportParams.employeeIds) && exportParams.employeeIds.length > 0) {
      exportParams.employeeIds = exportParams.employeeIds.join(',')
    } else {
      exportParams.employeeIds = undefined
    }

    const data = await ContractInfoApi.exportContractInfo(exportParams)
    download.excel(data, '老人签约信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(async () => {
  await getElderProfiles()
  await getUserList()
  await getList()
})
</script>
