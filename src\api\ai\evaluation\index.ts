import request from '@/config/axios'
import { getAccessToken } from '@/utils/auth'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { config } from '@/config/axios/config'

// AI 评估分析请求 VO
export interface AiEvaluationAnalysisReqVO {
  modelId: number // 模型ID
  content: string // 分析内容
  evaluationResultId?: number // 评估结果ID
  stream?: boolean // 是否流式响应
}

// AI 评估分析响应 VO
export interface AiEvaluationAnalysisRespVO {
  content: string // 分析内容
  status: string // 分析状态
  finished: boolean // 是否完成
}

// AI 评估分析 API
export const AiEvaluationApi = {
  // 生成评估分析（流式）
  generateAnalysisStream: ({
    data,
    onMessage,
    onError,
    onClose,
    ctrl
  }: {
    data: AiEvaluationAnalysisReqVO
    onMessage?: (res: any) => void
    onError?: (...args: any[]) => void
    onClose?: (...args: any[]) => void
    ctrl: AbortController
  }) => {
    const token = getAccessToken()
    return fetchEventSource(`${config.base_url}/ai/evaluation/analysis-stream`, {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      },
      openWhenHidden: true,
      body: JSON.stringify(data),
      onmessage: onMessage,
      onerror: onError,
      onclose: onClose,
      signal: ctrl.signal
    })
  },

  // 生成评估分析（非流式）
  generateAnalysis: async (data: AiEvaluationAnalysisReqVO) => {
    return await request.post({ url: `/ai/evaluation/analysis`, data })
  }
}
