<template>
  <div class="elder-info-card">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="header-title">老人信息</span>
          <el-button
            type="primary"
            link
            :icon="isCollapsed ? 'ArrowDown' : 'ArrowUp'"
            @click="toggleCollapse"
          >
            {{ isCollapsed ? '展开' : '收起' }}
          </el-button>
        </div>
      </template>
      <div v-loading="loading">
        <el-collapse-transition>
          <div v-show="!isCollapsed">
            <div v-if="elderInfo" class="elder-content">
              <div class="elder-avatar">
                <el-avatar
                  :size="100"
                  :src="elderInfo.avatar || defaultAvatar"
                  fit="cover"
                >
                  {{ elderInfo.name ? elderInfo.name.substring(0, 1) : '?' }}
                </el-avatar>
              </div>
              <div class="elder-details">
                <div class="elder-basic-info">
                  <h3 class="elder-name">{{ elderInfo.name }}</h3>
                  <div class="elder-tags">
                    <el-tag size="small" type="info">{{ elderInfo.gender === 1 ? '男' : '女' }}</el-tag>
                    <el-tag size="small" type="info" class="ml-5">{{ elderInfo.age || '未知' }}岁</el-tag>
                  </div>
                </div>
                
                <el-descriptions :column="2" border size="small" class="mt-10">
                  <el-descriptions-item label="身份证号">
                    {{ elderInfo.idCard || '未知' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="出生日期">
                    {{ elderInfo.birthday ? formatDate(elderInfo.birthday) : '未知' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="房间号">
                    {{ elderInfo.roomName || '未分配' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="床位号">
                    {{ elderInfo.bedName || '未分配' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="入住日期" :span="2">
                    {{ elderInfo.checkInDate ? formatDate(elderInfo.checkInDate) : '未知' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="护理等级" :span="2">
                    {{ elderInfo.nursingLevelName || '未设置' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="紧急联系人" :span="2">
                    {{ elderInfo.emergencyContact || '未设置' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系电话" :span="2">
                    {{ elderInfo.emergencyPhone || '未设置' }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
            <div v-else class="no-elder-info">
              <el-empty description="未找到老人信息" />
            </div>
          </div>
        </el-collapse-transition>
        
        <div v-if="isCollapsed && elderInfo" class="elder-summary">
          <div class="summary-item">
            <span class="label">姓名:</span>
            <span class="value">{{ elderInfo.name }}</span>
          </div>
          <div class="summary-item">
            <span class="label">性别:</span>
            <span class="value">{{ elderInfo.gender === 1 ? '男' : '女' }}</span>
          </div>
          <div class="summary-item">
            <span class="label">年龄:</span>
            <span class="value">{{ elderInfo.age || '未知' }}岁</span>
          </div>
          <div class="summary-item">
            <span class="label">房间:</span>
            <span class="value">{{ elderInfo.roomName || '未分配' }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, defineProps, watch } from 'vue'
import { ArchivesProfileApi, ArchivesProfileVO } from '@/api/elderArchives/archivesProfile'
import { formatDate } from '@/utils/formatTime'
import { useMessage } from '@/hooks/web/useMessage'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'

defineOptions({ name: 'ElderInfoCard' })

const props = defineProps({
  elderId: {
    type: Number,
    required: true
  }
})

// 默认头像
const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'

// 消息提示
const message = useMessage()

// 老人信息
const elderInfo = ref<ArchivesProfileVO | null>(null)
const loading = ref(false)
const isCollapsed = ref(false)

// 获取老人信息
const getElderInfo = async () => {
  if (!props.elderId) return
  
  loading.value = true
  try {
    const res = await ArchivesProfileApi.getArchivesProfile(props.elderId)
    elderInfo.value = res
  } catch (error) {
    console.error('获取老人信息失败:', error)
    message.error('获取老人信息失败')
  } finally {
    loading.value = false
  }
}

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

// 监听elderId变化
watch(() => props.elderId, (newVal) => {
  if (newVal) {
    getElderInfo()
  } else {
    elderInfo.value = null
  }
}, { immediate: true })

onMounted(() => {
  if (props.elderId) {
    getElderInfo()
  }
})
</script>

<style lang="scss" scoped>
.elder-info-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-title {
      font-size: 16px;
      font-weight: bold;
    }
  }
  
  .elder-content {
    display: flex;
    flex-direction: column;
    
    .elder-avatar {
      display: flex;
      justify-content: center;
      margin-bottom: 15px;
    }
    
    .elder-details {
      .elder-basic-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 15px;
        
        .elder-name {
          margin: 0 0 10px 0;
          font-size: 18px;
        }
        
        .elder-tags {
          display: flex;
        }
      }
    }
  }
  
  .no-elder-info {
    padding: 20px 0;
  }
  
  .elder-summary {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    
    .summary-item {
      margin-right: 15px;
      
      .label {
        color: #909399;
        margin-right: 5px;
      }
      
      .value {
        font-weight: bold;
      }
    }
  }
  
  .mt-10 {
    margin-top: 10px;
  }
  
  .ml-5 {
    margin-left: 5px;
  }
}
</style>
